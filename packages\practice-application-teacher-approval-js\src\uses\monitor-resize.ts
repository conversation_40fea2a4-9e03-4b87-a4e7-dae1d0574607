import { throttle } from 'lodash';


/**
 *  监听当前 窗口尺寸是否为移动端
 */
export const useMonitorResizeListener = (
  callback: (isMobile: boolean) => void,
) => {
  /** 移动端最大尺寸 */
  const MOBILE_SIZE = 768;
  /** 节流时间 */
  const THROTTLE_TIME = 300;

  // 初始化时立即调用一次回调函数
  callback(window.innerWidth < MOBILE_SIZE);

  // 使用节流函数包装resize处理函数，默认300ms内只执行一次
  const handleResize = throttle(() => {
    callback(window.innerWidth < MOBILE_SIZE);
  }, THROTTLE_TIME);

  window.addEventListener('resize', handleResize);


  // 返回清理函数，用于移除事件监听器
  return () => {
    window.removeEventListener('resize', handleResize);
    // 取消未执行的节流函数
    handleResize.cancel();
  };
};
