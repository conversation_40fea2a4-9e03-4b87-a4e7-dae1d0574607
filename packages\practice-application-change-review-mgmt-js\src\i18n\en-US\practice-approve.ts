export default {
  title: {
    practiceApprove: 'Practice application review',
    reject: 'Rejected',
    pass: 'Approved',
  },

  searchBar: {
    status: 'Review status',
    statusPlace: 'All review statuses',
    lockStatus: 'Lock status',
    lockStatusPlace: 'All lock statuses',
    keyword: 'Please enter student ID or name',
    userName: 'Student name',
    userNo: 'Student ID',
    practiceProgNo: 'Course code',
    cultivationProject: 'Training program name',
    practiceUnit: 'The institution where you practiced',
    practicePlace: 'Location',
    applyTime: 'Application time',
  },

  action: {
    cancel: '@:common.action.cancel',
    reject: 'Rejected',
    pass: 'Approved',
    view: 'View',
    approve: 'Review',
    unlock: 'Agree to unlock',
  },

  columns: {
    nameAndNo: 'Student name / student ID',
    projectInfo: 'Training program / Course code',
    status: 'Review status',
    practiceTime: 'Start / End time',
    practiceWeeks: 'Total weeks',
    practiceUnit: 'Institution where you practice/ Location',
    onCampusSupervisorName: 'On-campus supervisor',
    offCampusSupervisorName: 'Off-campus supervisor',
    creTime: 'First application time',
    updInfo: 'Last operated by / Time',
    lockStatus: 'Lock status',
  },

  hint: {
    saveSucc: 'Saved successfully',
    passConfirm: 'Confirm approval?',
    unlockTitle: 'Agree to unlock',
    unlockConfirm: 'Do you agree to unlock?',
    unlockSuccess: 'Unlock successfully',
  },

  label: {
    rejectReason: 'Reason of rejection ',
  },

  placeholder: {
    rejectReason: 'Input content',
  },
};
