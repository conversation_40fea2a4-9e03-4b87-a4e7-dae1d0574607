import type { InternalAxiosRequestConfig } from 'axios';
import { useI18nStore } from '@/store/i18n';

export function interceptorLocale() : (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig {
  const i18nStore = useI18nStore();

  return (config: InternalAxiosRequestConfig) => {
    const params = {
      ...config.params,
      lang: i18nStore.locale,
    };
    Object.assign(config, { params });

    return config;
  };
}
