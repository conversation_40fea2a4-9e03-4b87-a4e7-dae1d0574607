{"name": "practice-process-record-review-js", "description": "专业实践管理系统-实践过程记录审批", "version": "1.0.0", "private": true, "scripts": {"dev": "vite --config vite.config.dev.ts", "build": "vite build --config vite.config.prod.ts --mode production", "preview": "vite preview --config vite.config.dev.ts", "lint": "eslint --ext .js --ext .ts --ext .vue ./", "lint:css": "stylelint ./src/**/*.{css,less,vue}"}, "dependencies": {}}