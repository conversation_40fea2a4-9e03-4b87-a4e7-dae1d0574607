import { defineConfig, splitVendorChunkPlugin, loadEnv } from 'vite';
import path from 'path';
import URI from 'urijs';
import vue from '@vitejs/plugin-vue';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import qiankun from 'vite-plugin-qiankun';


function getPublicPath(envPublicPath: string) {
  let result = envPublicPath ?? '/';

  if (!result.endsWith('/')) {
    result += '/';
  }

  return result;
}

function getServiceUrl(envServiceUrl: string, port: number) {
  if (!envServiceUrl) {
    return (port === 80) ? 'http://localhost' : `http://localhost:${port}`;
  }

  return envServiceUrl;
}

function getBase(serviceUrl: string, publicPath: string, port: number) {
  const uri = new URI(serviceUrl);

  uri.segment(publicPath);

  if (!uri.port()) {
    uri.port(String(port));
  }

  return uri.toString();
}

function getHost(serviceUrl: string) {
  const uri = new URI(serviceUrl);

  return uri.hostname();
}

function getOutDir(publicPath: string) {
  const uri = new URI(publicPath);
  return path.resolve(__dirname, `../../dist/${uri.segment(-2)}`);
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const publicPath = getPublicPath(env.VITE_PUBLIC_PATH);
  const port = Number(env.VITE_PORT);
  const serviceUrl = getServiceUrl(env.VITE_SERVICE_URL, port);
  const base = getBase(serviceUrl, publicPath, port);
  const host = getHost(serviceUrl);
  const outDir = getOutDir(publicPath);

  return {
    base,
    server: {
      port,
      host,
      cors: true,
      origin: serviceUrl,
    },
    preview: {
      port,
      host,
      cors: true,
      strictPort: true,
    },
    build: {
      chunkSizeWarningLimit: 1500,
      emptyOutDir: true,
      outDir,
    },
    css: {
      postcss: {
        plugins: [
          require('postcss-preset-env'),
          require('postcss-less'),
        ],
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '~': path.resolve('./node_modules'),
      },
    },
    plugins: [
      vue(),
      cssInjectedByJsPlugin(),
      qiankun(path.basename(__dirname), {
        useDevMode: true,
      }),
      splitVendorChunkPlugin(),
    ],
  };
});
