import { namespaceT } from '@/helps/namespace-t';

export interface WarningModelType {
  visible: boolean;
  title: string;
  content: string;
  loading: boolean;
  id?: number;
  [key: string]: unknown;
}


export const createWarningModel = ({ title = '', content = '' }): WarningModelType => {
  return {
    visible: false,
    title: title || '',
    content: content || '',
    loading: false,
    id: undefined,
  };
};
