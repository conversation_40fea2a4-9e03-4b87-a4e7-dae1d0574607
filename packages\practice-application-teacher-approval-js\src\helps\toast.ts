import { Message } from 'view-ui-plus';


/**
 * // openToastLoading usage:
 *
 * const close = openToastLoading('loading-text', 1500)
 * ...
 * doSomething() {
 *  ...
 *  ...
 *  close()
 *  // or
 *  close().then(() => {
 *    console.log('loading has been closed')
 *  })
 * }
 */
export function openToastLoading(content: string, minAliveTime: number) {
  const destroy = Message.loading({
    content,
    duration: 0,
  });

  let executed = false;
  let closePromise = null;
  const close = () => {
    if (!executed) {
      executed = true;

      closePromise = new Promise<void>((resolve) => {
        setTimeout(() => {
          destroy();
          resolve();
        }, minAliveTime);
      });
    }

    return closePromise;
  };

  return close;
}

export function openToastError(content: string) {
  Message.error({ content });
}

export function openToastWarning(content: string) {
  Message.warning({ content });
}

export function openToastSuccess(content: string) {
  Message.success({ content });
}
