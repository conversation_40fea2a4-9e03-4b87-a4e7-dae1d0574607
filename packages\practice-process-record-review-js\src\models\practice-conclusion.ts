import type { PracticeConclusionModelType } from '@/types/practice-conclusion';

export function createPracticeConclusionModel(): PracticeConclusionModelType {
  return {
    id: null,
    practiceApplyRecId: null,
    userNo: null,
    userName: null,
    practiceTitle: null,
    practiceUnit: null,
    cultivationProject: null,
    tutor: null,
    practiceStartDate: null,
    practiceEndDate: null,
    practiceProgNo: null,
    practiceAim: '',
    practiceContent: '',
    practiceAchievement: '',
    attachmentList: [],
    attachmentIdList: [],
    submitUserName: null,
    submitTime: null,
    status: null,
    canRemove: false,
    canApplyUpdate: false,
    canReSubmit: false,
    canUpdate: false,
    canPrint: false,
    modifyContent: null,
    canApproval: false,
  };
}
