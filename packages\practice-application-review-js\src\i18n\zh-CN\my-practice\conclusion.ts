export default {
  columns: {
    practiceTitle: '实践题目',
    practiceProgNo: '@:myPractice.applyMod.columns.practiceProgNo',
    researchDirection: '@:myPractice.applyMod.columns.researchDirection',
    practiceUnit: '@:myPractice.applyMod.columns.practiceUnit',
    practicePlace: '@:myPractice.applyMod.columns.practicePlace',
    changeStatus: '@:myPractice.applyMod.columns.changeStatus',
    createInfo: '@:myPractice.applyMod.columns.createInfo',
  },

  action: {
    view: '@:myPractice.applyMod.action.view',
    addConclusion: '点击添加实践总结',
    cancel: '@:common.action.cancel',
    save: '@:common.action.save',
    modify: '@:common.action.modify',
    draft: '@:myPractice.process.action.draft',
    submit: '@:myPractice.process.action.submit',
    pass: '@:myPractice.process.action.pass',
    reject: '@:myPractice.process.action.reject',
  },

  title: {
    add: '添加实践总结',
    practiceConclusion: '实践总结',
    edit: '@:myPractice.process.title.edit',
    view: '@:myPractice.process.title.view',
    restart: '@:myPractice.process.title.restart',
    changeApply: '@:myPractice.process.title.changeApply',
    actionLogTitle: '实践总结流转情况',
    changeActionLogTitle: '实践总结异动流转情况',
  },

  hint: {
    saveDraftSucc: '@:myPractice.process.hint.saveDraftSucc',
    submitSucc: '@:myPractice.process.hint.submitSucc',
    delSuccess: '@:myPractice.process.hint.delSuccess',
    delTitle: '@:myPractice.process.hint.delTitle',
    delConfirm: '@:myPractice.process.hint.delConfirm',
    approveSuccess: '@:myPractice.process.hint.approveSuccess',
    saveSucc: '@:myPractice.process.hint.saveSucc',
  },

  label: {
    userNo: '@:myPractice.process.label.userNo',
    userName: '@:myPractice.process.label.userName',
    practiceTitle: '实践题目',
    practiceUnit: '实践单位',
    practiceProgNo: '@:practiceApply.form.label.practiceProgNo',
    cultivationProject: '@:practiceApply.form.label.cultivationProject',
    supervisor: '指导老师',
    practiceTime: '实践起止时间',
    practiceAim: '实践目的及意义',
    practiceContent: '实践的主要内容',
    practiceAchievement: '实践主要成果',
    modifyContent: '@:practiceApply.form.label.modifyContent',
    attachment: '@:myPractice.process.label.attachment',
  },

  placeholder: {
    modifyContent: '@:practiceApply.form.placeholder.modifyContent',
  },

  print: {
    title: '清华大学国际研究生院-研究生专业实践总结报告',

    coverFooter: '清华大学国际研究生院制',
    tip: '注：专业实践结束后 ，此表于毕业预申请前交培养处实验实践教学中心存档。',

    tableTitle: [
      '一、实践目的及意义',
      '二、实践的主要内容',
      '三、实践主要成果',
      '实践总结附件：',
      '四、学生本人声明',
      '五、专业实践评审意见',
    ],

    label: {
      projectName: '题目',
      userName: '姓名',
      userNo: '学号',
      mainFields: '专业工程领域',
      supervisor: '导师',
      practiceUnit: '实践单位',
      practiceTime: '实践起止时间',
      practiceProgNo: '专业实践课程号',
      applyTime: '撰写日期',
      onCampusRemark: '校内导师意见：',
      offCampusRemark: '校外导师意见：',
      businessRemark: '实践基地（单位）意见：',
      projectRemark: '项目指导委员会评审意见：',
    },

    text: {
      attachmentRefer: '请参考附件',
      statement: '本人声明报告内容严格遵守学校和实践单位有关知识产权的保护规定，遵守学术职业道德规范，未抄袭他人实践成果。',
      sign: '（签字 ）：',
      seal: '（签章）：',
      time: [
        '年', '月', '日',
      ],
      score: '成绩评定：',
      qualified: '合格',
      unqualified: '不合格',
      inChargeSign: '负责人签字：',
      participantSign: '参评人员签字：',
    },
  },
};
