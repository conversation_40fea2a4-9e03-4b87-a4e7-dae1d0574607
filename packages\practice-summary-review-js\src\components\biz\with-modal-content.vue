<script lang="ts" setup>
import ButtonGroup from '@/components/biz/button-group.vue';
import ActionLog from '@/components/biz/action-log.vue';
import PracticeConclusionDetail from '@/components/biz/practice-conclusion-detail.vue';

import { namespaceT } from '@/helps/namespace-t';
import type { PracticeConclusionModelType } from '@/types/practice-conclusion';
import { ActionLogType } from '@/types/action-log';

type Props = {
  loading: boolean;
  modelValue: PracticeConclusionModelType;
  actionLog: ActionLogType[];
  isApprove: boolean;
};
withDefaults(defineProps<Props>(), {
  loading: false,
});
const emit = defineEmits(['on-cancel', 'on-reject', 'on-pass']);

const t = namespaceT('myPractice.conclusion');
</script>


<template>
  <div class="with-modal-content">
    <div class="modal-form-content with-action-log">
      <PracticeConclusionDetail
        :model-value="modelValue"
        is-teacher
      />

      <ActionLog
        :data-source="actionLog"
        :title="t('title.actionLogTitle')"
      />
    </div>

    <div
      v-if="isApprove"
      class="footer"
    >
      <ButtonGroup
        @on-cancel="emit('on-cancel')"
        @on-reject="emit('on-reject')"
        @on-pass="emit('on-pass')"
      />
    </div>
  </div>
</template>


<style scoped lang="less">
.footer {
  width: 100%;
  padding: 16px;
  text-align: center;
}
</style>

<style lang="less">
.ivu-message {
  z-index: 1600 !important;
}
</style>
