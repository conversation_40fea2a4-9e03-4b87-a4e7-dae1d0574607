import detail from './detail';
import applyMod from './apply-mod';
import process from './process';
import conclusion from './conclusion';
import review from './review';


export default {
  searchBar: {
    keyword: 'Enter Advisor,Industry Mentor,Placement',
  },

  title: {
    practiceContentManage: '@:myPractice.action.practiceContentManage',
    changeLogTitle: 'Practice application form change workflow status',
  },

  action: {
    feedback: 'Professional practice issue feedback',
    practiceContentManage: 'Practice content management',
  },

  columns: {
    practiceProgNo: '@:practiceApply.columns.practiceProgNo',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    cultivationProject: '@:practiceApply.columns.cultivationProject',
    practiceUnit: '@:practiceApply.columns.practiceUnit',
    practicePlace: '@:practiceApply.columns.practicePlace',
    practiceStartDate: 'Professional practice start date',
    practiceEndDate: 'Professional practice end date',
  },

  detail,
  applyMod,
  process,
  conclusion,
  review,
};
