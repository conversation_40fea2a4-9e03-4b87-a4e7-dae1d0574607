<script setup lang="ts">
import { computed, onMounted, nextTick } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import type { PracticeApplyBaseInfoType } from '@/types/practice';
import { YoN } from '@/consts/y-o-n';

import { usePracticeProjectStore } from '@/store/practice-project';
import { useClassNoStore } from '@/store/classno';

import DetailLabelItem from '@/components/common/detail-label-item.vue';

const props = withDefaults(defineProps<{
  baseInfo: PracticeApplyBaseInfoType
}>(), {});


const model = computed(() => props.baseInfo);
const tl = namespaceT('practiceApply.form.label');
const tc = namespaceT('consts');

const projectStore = usePracticeProjectStore();
const classNoStore = useClassNoStore();

onMounted(async () => {
  await nextTick();
  projectStore.loadDataIfNeeded();
  classNoStore.loadDataIfNeeded();
});

</script>


<template>
  <div class="pl-20">
    <DetailLabelItem
      :label="tl('userNo')"
      :label-width="120"
    >
      {{ model.userNo }}
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('userName')"
      :label-width="120"
    >
      {{ model.userName }}
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('mobile')"
      :label-width="120"
    >
      <template v-if="model.phone">
        {{ model.phone }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('email')"
      :label-width="120"
    >
      <template v-if="model.email">
        {{ model.email }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('cultivationProject')"
      :label-width="120"
    >
      <template v-if="model.cultivationProject">
        {{ projectStore.getTextByCode(model.cultivationProject) }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('freshGraduateInd')"
      :label-width="120"
    >
      {{ model.freshGraduateInd === YoN.Y ? tc('yon.y') : tc('yon.n') }}
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('practiceProgNo')"
      :label-width="120"
    >
      <template v-if="model.practiceProgNo">
        {{ classNoStore.getTextByCode(model.practiceProgNo) }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('tutorName')"
      :label-width="120"
    >
      <template v-if="model.supervisor">
        {{ model.supervisor }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('researchDirection')"
      :label-width="120"
    >
      <template v-if="model.researchDirection">
        {{ model.researchDirection }}
      </template>
    </DetailLabelItem>
  </div>
</template>
