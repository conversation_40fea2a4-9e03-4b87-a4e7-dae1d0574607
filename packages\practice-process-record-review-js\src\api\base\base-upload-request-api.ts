import { type RequestMethod, type RequestParams, type RequestData, BaseRequestApi } from './base-request-api';

export { type RequestMethod, type RequestParams, type RequestData };


export class BaseUploadRequest<PERSON>pi<T> extends BaseRequestApi<T> {
  constructor(args) {
    super({
      ...args,
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  }

  method(): RequestMethod {
    return 'POST';
  }
}
