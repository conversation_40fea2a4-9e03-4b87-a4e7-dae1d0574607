<script lang="ts" setup>
import { onMounted, nextTick } from 'vue';

import type { PracticeProcessModelType } from '@/types/practice-process';
import { namespaceT } from '@/helps/namespace-t';
import { useClassNoStore } from '@/store/classno';

import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import DetailLabelItem from '@/components/common/detail-label-item.vue';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';
import AttachmentListView from '@/components/biz/attachment-list-view.vue';
import CellTitle from '@/components/biz/cell-title.vue';
import DefaultText from '@/components/common/default-text.vue';

withDefaults(defineProps<{
  changeApply?: boolean;
  isManage?: boolean; // 是否管理端
  isTeacher?: boolean; // 是否教师端
}>(), {
  changeApply: false,
  isManage: false,
  isTeacher: false,
});

const model = defineModel<PracticeProcessModelType>();
const t = namespaceT('myPractice.process');
const td = namespaceT('dateFormat');
const tl = namespaceT('myPractice.process.label');

const classNoStore = useClassNoStore();

onMounted(async () => {
  await nextTick();
  classNoStore.loadDataIfNeeded();
});
</script>


<template>
  <WrapperFormTitle :title="t('title.practiceProcess')">
    <div class="pl-20">
      <DetailLabelItem
        :label="tl('practiceProgNo')"
      >
        <template v-if="model.practiceProgNo">
          {{ classNoStore.getTextByCode(model.practiceProgNo) }}
        </template>
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('userNo')"
      >
        {{ model.userNo }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('userName')"
      >
        {{ model.userName }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('projectName')"
      >
        {{ model.projectName }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('startDate')"
      >
        {{ $dateFormatSTZ(model.startTime, td('date')) }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('endDate')"
      >
        {{ $dateFormatSTZ(model.endTime, td('date')) }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('content')"
      >
        <PimaSanitizeHtml
          :inner-html="model.content"
          class="view"
        />
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('attachment')"
      >
        <AttachmentListView
          :attachments="model.attachmentList"
        />
      </DetailLabelItem>

      <!-- 修改内容说明 -->
      <template v-if="changeApply">
        <CellTitle
          :title="tl('modifyContent')"
          class="mt-20"
        />
        <DetailLabelItem>
          <div
            :class="{ 'noticeable': !!model.modifyContent }"
          >
            <DefaultText :text="model.modifyContent" />
          </div>
        </DetailLabelItem>
      </template>
    </div>
  </WrapperFormTitle>
</template>


<style lang="less" scoped>
.noticeable {
  color: var(--red-color);
}
</style>
