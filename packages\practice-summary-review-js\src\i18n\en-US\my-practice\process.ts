export default {
  columns: {
    projectName: '@:myPractice.process.label.projectName',
    cultivationProject: '@:myPractice.applyMod.columns.cultivationProject',
    practiceProgNo: '@:myPractice.applyMod.columns.practiceProgNo',
    researchDirection: '@:myPractice.applyMod.columns.researchDirection',
    practiceUnit: '@:myPractice.applyMod.columns.practiceUnit',
    practicePlace: '@:myPractice.applyMod.columns.practicePlace',
    practiceStartDate: '@:myPractice.applyMod.columns.practiceStartDate',
    practiceEndDate: '@:myPractice.applyMod.columns.practiceEndDate',
    changeStatus: '@:myPractice.applyMod.columns.changeStatus',
    createInfo: '@:myPractice.applyMod.columns.createInfo',
  },

  action: {
    view: '@:myPractice.applyMod.action.view',
    applyEdit: '@:practiceApply.action.applyEdit',
    print: '@:practiceApply.action.print',
    resubmit: '@:practiceApply.action.resubmit',
    draft: '@:practiceApply.action.draft',
    submit: '@:practiceApply.action.submit',
    edit: '@:common.action.edit',
    cancel: '@:common.action.cancel',
    delete: '@:common.action.delete',
    addProcess: 'Click to add practice process record',
    approve: 'Review',
    pass: 'Approved',
    reject: 'Rejected',
    modify: '@:common.action.modify',
    save: '@:common.action.save',
    review: 'Evaluate',
  },

  hint: {
    submitSucc: '@:practiceApply.hint.submitSucc',
    saveDraftSucc: '@:practiceApply.hint.saveDraftSucc',
    delTitle: 'Confirm to delete',
    delConfirm: 'The data cannot be retrieved once deleted. Do you still want to delete it?',
    delSuccess: 'Deleted successfully',
    approveSuccess: 'Reviewed successfully',
    saveSucc: 'Saved successfully',
    draftDataExists: 'A “draft” of the current practice process record already exists and cannot be added',
  },

  label: {
    submitPerson: 'Submitted by',
    submitTime: 'Submission time',
    practiceProgNo: 'Course code',
    userNo: 'Student ID',
    userName: 'Name',
    projectName: 'Practice project title',
    startDate: 'Start time',
    endDate: 'End time',
    content: 'Content description',
    attachment: 'Attachment',
    modifyContent: '@:practiceApply.form.label.modifyContent',
  },

  placeholder: {
    input: '@:common.placeholder.input',
    select: '@:common.placeholder.select',
    modifyContent: '@:practiceApply.form.placeholder.modifyContent',
  },

  title: {
    add: 'Add practice process record',
    practiceProcess: 'Practice process record',
    edit: 'Edit',
    modify: '@:common.action.modify',
    view: 'View',
    actionLogTitle: 'Practice process record workflow status',
    changeActionLogTitle: 'Practice process change workflow status',
    restart: 'Re-initiate',
    changeApply: 'Request modification',
    pass: 'Confirm approval',
  },

  print: {
    title: 'Professional Practice Record in Tsinghua Shenzhen International Graduate School',
    processPeriod: 'Practice period: ',
    content: 'Practice content: (for students)',
    sign: '(signature): ',
    attachPage: '(attach separate table if needed)',
    offCampusRemark: 'External Co-supervisor comments: ',
    onCampusRemark: 'Primary supervisor comments: ',
    tip: 'Note: this form must be completed once every four weeks.',
  },
};
