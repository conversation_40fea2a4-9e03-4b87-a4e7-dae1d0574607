import type { ActionLogType } from '@/types/action-log';
import { CommonApi, RequestParams } from '@/api/common/common-api';
import { namespaceT } from '@/helps/namespace-t';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
});

export class TeacherApprovalActionLogApi extends CommonApi<{ data: Array<ActionLogType> }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/teacher/practice-summary-approvers/${this.id}/action-logs`;
  }

  defaultParams(): RequestParams {
    return {
      page: 1,
      limit: 99999,
    };
  }

  async send(): Promise<{ data: ActionLogType[] }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.conclusionSubmit');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
