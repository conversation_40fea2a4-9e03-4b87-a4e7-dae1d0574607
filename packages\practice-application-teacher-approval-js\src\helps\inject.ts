import URI from 'urijs';

const ENGINEERING_THEME_IVIEW_CSS_ID = 'engineering-theme-iview-css';

function getStaticResourcesBaseUri() {
  return new URI(import.meta.env.VITE_STATIC_RESOURCES_BASE_URL);
}

// engineering-theme-iview 主题样式文件地址
function getEngineeringThemeIViewCssUrl() {
  const uri = getStaticResourcesBaseUri();
  uri.segment('css');
  uri.segment('engineering-theme-iview');
  uri.segment(import.meta.env.VITE_THEME_IVIEW_VERSION || 'latest');
  uri.segment('default.min.css');
  if (import.meta.env.VITE_THEME_IVIEW_FLAG) {
    uri.search(import.meta.env.VITE_THEME_IVIEW_FLAG);
  }

  return uri.toString();
}

export function injectEngineeringThemeIViewCss() {
  const linkUrl = getEngineeringThemeIViewCssUrl();
  const linkElement = document.createElement('link');
  linkElement.href = linkUrl;
  linkElement.rel = 'stylesheet';
  linkElement.id = ENGINEERING_THEME_IVIEW_CSS_ID;
  linkElement.type = 'text/css';
  if (!document.getElementById(ENGINEERING_THEME_IVIEW_CSS_ID)) {
    document.head.appendChild(linkElement);
  }
}

export function removeEngineeringThemeIViewCss() {
  const linkElement = document.getElementById(ENGINEERING_THEME_IVIEW_CSS_ID);
  if (linkElement) {
    linkElement.remove();
  }
}

export function injectViewUiPlusCss() {
  import('view-ui-plus/dist/styles/viewuiplus.css');
}
