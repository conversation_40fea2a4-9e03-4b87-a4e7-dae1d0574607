export default {
  action: {
    search: 'Search',
    advancedSearch: 'Advanced Search',
    reset: 'Reset',
    cancel: 'Cancel',
    confirm: 'Confirm',
    ok: 'OK',
    selectFile: 'Select File',
    downloadImportTemplate: 'Download Import Template',
    add: 'Add',
    save: 'Save',
    import: 'Import',
    export: 'Export',
    delete: 'Delete',
    batchDelete: 'Batch Delete',
    edit: 'Edit',
    back: 'Back',
    close: 'Close',
    reject: 'Rejected',
    pass: 'Approved',
    view: 'View',
    upload: 'Click to upload',
    reUpload: 'Re-upload',
    modify: 'Modify',
    submit: 'Submit',
  },

  table: {
    serial: 'Serial',
    createdTime: 'Created time',
    operation: 'Operation',
    operatorAndTime: 'Operated by / Operation time',
    checkAll: 'Select all results in list (total: {total} entries)',
  },

  placeholder: {
    search: 'Search',
    select: 'Please select',
    input: 'Please enter',
    all: 'All',
    startDate: 'Start Date',
    endDate: 'End Date',
    startTime: 'Start Time',
    endTime: 'End Time',
    pleaseSelect: 'Please select',
    pleaseInput: 'Please enter',
    multiKeywordsPlace: 'Use "\\" to separate multiple search terms',
  },

  error: {
    thisFieldIsRequired: 'This field is required',
    thisFieldMustBeSelected: 'This field must be selected',
    formatIsIncorrect: 'Format is incorrect',
    emailFormatIsIncorrect: 'Email format is incorrect',
    mobileNumberFormatIsIncorrect: 'Mobile Number format is incorrect',
    excelTemplateError: 'Excel template error',
    dataDoesNotExist: 'Data does not exist',
    dataError: 'Data error. Please select again.',
    timeoutError: 'Your request timed out. Please refresh and try again.',
    unknownError: 'Sorry, the program encountered an unknown error, please refresh and try again.',
    networkError: 'Network error, please refresh and try again',
    noAuth: 'Sorry, you do not have permission to access this page.',
    noData: 'No data available',
    fileTypeError: 'Invalid file type, please upload files of type {accept}',
    fileSizeError: 'Uploaded file must be no larger than {size} MB, please re-upload',
    fileNumError: 'Number of files cannot exceed {size}',
  },

  hint: {
    hint: 'Notice',
    loading: 'Loading',
    noSearchResult: 'No search results available',
    deleting: 'Deleting...',
    dataSaved: 'Record saved',
    savingFailed: 'Failed to save',
    successfullyDeleted: 'Record deleted',
    deletionFailed: 'Failed to delete',
    downloadSuccessful: 'File downloaded',
    downloadFailed: 'Failed to download',
    importSucceeded: 'List imported',
    importFailed: 'Failed to imported',
    exportSucceeded: 'List exported',
    exportFailed: 'Failed to export',
    uploadCompleted: 'File uploaded',
    failedToUpload: 'Failed to upload',
  },

  modal: {
    reminder: 'Reminder',
    areYouSureToDelete: 'Confirm delete?',
  },

  operationInfo: {
    lastEditBy: 'Last edited',
    createdBy: 'Created by',
  },
};
