<script setup lang="ts">
import { ref, nextTick, onMounted, onBeforeUnmount } from 'vue';

import TitleBar from '^/components/common/title-bar.vue';
import WrapperForm from '^/components/common/wrapper-form.vue';

import TheTheme from '@/components/the-theme.vue';


import { namespaceT } from '@/helps/namespace-t';
import { useQiankunStore } from '@/store/qiankun';
import { useMonitorResizeListener } from '@/uses/monitor-resize';


const t = namespaceT('doubleSelection');


const loading = ref(false);
const isMobile = ref(false);

const qiankunStore = useQiankunStore();
// const { dataId } = storeToRefs(qiankunStore);
const resizeListenerCancel = useMonitorResizeListener((_isMobile) => {
  isMobile.value = _isMobile;
});

// const approvalChannel = computed(() => {
//   return isMobile.value ? ApprovalChannel.MINIPROGRAM : ApprovalChannel.PC;
// });

const goBack = () => qiankunStore.goBack();


onMounted(async () => {
  await nextTick();
  // fetchAll();
});

onBeforeUnmount(() => {
  resizeListenerCancel();
});

</script>


<template>
  <TheTheme>
    <div
      class="pima-form-page double-selection-detail"
      :class="{'to-do': qiankunStore.isToDo }"
    >
      <TitleBar
        go-back
        :title="t('title.detail')"
        @go-back="goBack"
      />
      <WrapperForm :loading="loading">
        <!-- <NavFormWrap :nav-bar="null">
          <WrapperFormTitle :title="t('title.detail')">
            <DetailForm
              v-model="model"
              :is-mobile="isMobile"
            />
          </WrapperFormTitle>

          <template #right>
            <ActionLog
              :data-source="actionLog"
              :title="t('actionLog.title')"
              :empty-text="t('actionLog.emptyText',{title:t('actionLog.title')})"
            />
          </template>
        </NavFormWrap> -->

        <!-- <template
          v-if="model.canApproval"
          #action
        >
          <Button
            class="pima-btn mr-15"
            type="default"
            :disabled="passModel.loading || rejecting"
            @click="goBack"
          >
            {{ tc('action.cancel') }}
          </Button>

          <Button
            class="pima-btn mr-15"
            type="primary"
            :disabled="passModel.loading"
            :loading="rejecting"
            @click="rejectModalVisible = true"
          >
            {{ t('action.reject') }}
          </Button>

          <Button
            class="pima-btn"
            type="primary"
            :disabled="rejecting"
            :loading="passModel.loading"
            @click="passModel.visible = true "
          >
            {{ t('action.pass') }}
          </Button>
        </template> -->
      </WrapperForm>
    </div>
  </TheTheme>
</template>
