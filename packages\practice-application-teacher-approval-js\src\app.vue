<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onBeforeUnmount, computed } from 'vue';
import { storeToRefs } from 'pinia';

import TheTheme from '@/components/the-theme.vue';
import TitleBar from '@/components/common/title-bar.vue';
import NavFormWrap from '@/components/common/nav-form-wrap.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import DetailForm from '@/components/biz/job-post/detail-form.vue';
import ActionLog from '@/components/biz/action-log.vue';
import WarningModal from '@/components/biz/modal/warning-modal.vue';
import RejectModal from '@/components/biz/modal/reject.vue';

import type { ActionLogType } from '@/types/action-log';

import { DetailApi } from '@/api/double-selection/detail';
import { ActionLogApi } from '@/api/double-selection/action-log';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';
import { useQiankunStore } from '@/store/qiankun';
import { createDetailModel } from '@/models/double-selection';
import { createPassModel } from './models/modal';
import { ApprovalAction } from './consts/approval-action';
import { ApprovalApi } from './api/double-selection/approval';
import { useMonitorResizeListener } from './uses/monitor-resize';

import { ApprovalChannel } from './consts/approval-channel';


const t = namespaceT('doubleSelection');
const tc = namespaceT('common');


const loading = ref(false);
const rejecting = ref(false);
const isMobile = ref(false);
const rejectModalVisible = ref(false);
const model = reactive(createDetailModel());
const passModel = reactive(createPassModel());
const actionLog = ref<ActionLogType[]>([]);

const qiankunStore = useQiankunStore();
const { dataId } = storeToRefs(qiankunStore);
const resizeListenerCancel = useMonitorResizeListener((_isMobile) => {
  isMobile.value = _isMobile;
});

const approvalChannel = computed(() => {
  return isMobile.value ? ApprovalChannel.MINIPROGRAM : ApprovalChannel.PC;
});

const goBack = () => qiankunStore.goBack();

const fetchDetail = async () => {
  const api = new DetailApi({ id: dataId.value });
  const res = await api.sendWithSpecifyType();
  Object.assign(model, res);
};


const fetchActionLogs = async () => {
  const api = new ActionLogApi<ActionLogType>({ id: dataId.value });
  const res = await api.send();
  actionLog.value = res.data;
};

const fetchAll = async () => {
  try {
    loading.value = true;
    await Promise.all([fetchDetail(), fetchActionLogs()]);
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};

const onApproval = async (action:ApprovalAction, remark:string = undefined) => {
  try {
    if (action === ApprovalAction.PASS) {
      passModel.loading = true;
    } else {
      rejecting.value = true;
    }

    const api = new ApprovalApi({ id: dataId.value });
    api.data = {
      remark,
      status: action,
      approvalChannel: approvalChannel.value,
    };


    await api.send();
    openToastSuccess(t('hint.operateSuccessfully'));
    passModel.visible = false;
    rejectModalVisible.value = false;
    goBack();
  } catch (error) {
    openToastError(error.message);
  } finally {
    passModel.loading = false;
    rejecting.value = false;
  }
};


onMounted(async () => {
  await nextTick();
  fetchAll();
});

onBeforeUnmount(() => {
  resizeListenerCancel();
});

</script>


<template>
  <TheTheme>
    <div
      class="pima-form-page double-selection-detail"
      :class="{'to-do': qiankunStore.isToDo }"
    >
      <TitleBar
        go-back
        :title="t('title.detail')"
        @go-back="goBack"
      />
      <WrapperForm :loading="loading">
        <NavFormWrap :nav-bar="null">
          <WrapperFormTitle :title="t('title.detail')">
            <DetailForm
              v-model="model"
              :is-mobile="isMobile"
            />
          </WrapperFormTitle>

          <template #right>
            <ActionLog
              :data-source="actionLog"
              :title="t('actionLog.title')"
              :empty-text="t('actionLog.emptyText',{title:t('actionLog.title')})"
            />
          </template>
        </NavFormWrap>

        <template
          v-if="model.canApproval"
          #action
        >
          <Button
            class="pima-btn mr-15"
            type="default"
            :disabled="passModel.loading || rejecting"
            @click="goBack"
          >
            {{ tc('action.cancel') }}
          </Button>

          <Button
            class="pima-btn mr-15"
            type="primary"
            :disabled="passModel.loading"
            :loading="rejecting"
            @click="rejectModalVisible = true"
          >
            {{ t('action.reject') }}
          </Button>

          <Button
            class="pima-btn"
            type="primary"
            :disabled="rejecting"
            :loading="passModel.loading"
            @click="passModel.visible = true "
          >
            {{ t('action.pass') }}
          </Button>
        </template>
      </WrapperForm>

      <WarningModal
        v-model="passModel.visible"
        class="warning-modal"
        :title="passModel.title"
        :content="passModel.content"
        :loading="passModel.loading"
        @on-cancel="passModel.visible = false"
        @on-confirm="onApproval(ApprovalAction.PASS)"
      />

      <RejectModal
        v-model="rejectModalVisible"
        class="reject-modal"
        :loading="rejecting"
        @on-cancel="rejectModalVisible = false"
        @on-confirm="(remark)=>onApproval(ApprovalAction.REJECT,remark)"
      />
    </div>
  </TheTheme>
</template>
