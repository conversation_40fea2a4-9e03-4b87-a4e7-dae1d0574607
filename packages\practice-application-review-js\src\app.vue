<script setup lang="ts">
import { ref, onMounted, reactive, nextTick, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import PracticeApplyDetail from '@/components/biz/practice-apply-detail';

import { PracticeApproveDetailApi } from '@/api/practice-approve/detail';
import { PracticeActionLogApi } from '@/api/practice-approve/action-log';

import { namespaceT } from '@/helps/namespace-t';
import { openToastError } from '@/helps/toast';
import { createPracticeDetailModel } from '@/models/practice-approve';
import type { ActionLogType } from '@/types/action-log';
import { useQiankunStore } from '@/store/qiankun';
import { handleDetailData } from '@/helps/handle-data';
import { appendEditorCss } from '@/helps/append-editor-css';

import RejectModal from '@/components/biz/reject-modal.vue';
import PassModal from '@/components/biz/pass-modal.vue';

const t = namespaceT('practiceApprove');
const loading = ref(false);
const model = reactive(createPracticeDetailModel());
const actionLog = ref<Array<ActionLogType>>([]);

const showPassModel = ref(false);
const showRejectModel = ref(false);
const qiankunStore = useQiankunStore();
const { dataId, isToDo } = storeToRefs(qiankunStore);

function onGoBack() {
  qiankunStore.goBack();
}

async function loadActionLog() {
  const api = new PracticeActionLogApi({ id: dataId.value });
  const res = await api.send();
  actionLog.value = res.data;
}

async function loadDetailsInfo() {
  try {
    loading.value = true;
    const api = new PracticeApproveDetailApi({ id: dataId.value });
    const res = await api.send();
    Object.assign(model, handleDetailData(res.model));
    await loadActionLog();
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
}

function onReject() {
  showRejectModel.value = true;
}

function onPass() {
  showPassModel.value = true;
}

onBeforeMount(() => {
  appendEditorCss();
});

onMounted(async () => {
  await nextTick();
  loadDetailsInfo();
});
</script>


<template>
  <div class="engineering-theme">
    <div
      class="pima-form-page"
      :class="{'to-do': isToDo}"
    >
      <TitleBar
        go-back
        :title="t('title.practiceApprove')"
        @go-back="onGoBack"
      />

      <WrapperForm
        :loading="loading"
      >
        <PracticeApplyDetail
          v-model="model.changePracticeApplyRecDetails"
          :action-log="actionLog"
        />

        <template
          v-if="model.canApprove"
          #action
        >
          <Button
            class="pima-btn mr-16"
            type="default"
            @click="onGoBack"
          >
            {{ t('action.cancel') }}
          </Button>
          <Button
            class="pima-btn mr-16"
            type="primary"
            @click="onReject"
          >
            {{ t('action.reject') }}
          </Button>
          <Button
            class="pima-btn"
            type="primary"
            @click="onPass"
          >
            {{ t('action.pass') }}
          </Button>
        </template>
      </WrapperForm>

      <RejectModal
        v-model="showRejectModel"
        :detail-id="qiankunStore.dataId"
        @on-success="onGoBack"
      />

      <PassModal
        v-model="showPassModel"
        :detail-id="qiankunStore.dataId"
        @on-success="onGoBack"
      />
    </div>
  </div>
</template>
