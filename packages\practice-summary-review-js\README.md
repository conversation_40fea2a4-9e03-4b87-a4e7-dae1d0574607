# 专业实践- 实践总结审批

## 安裝

### 编译依赖

本项目使用使用 [node](https://nodejs.org/) 和 [yarn](https://yarnpkg.com/)。请确保本地已安装它们。

- node 需要 [16.18.x](https://nodejs.org/en/download/) 或以上TLS版本（v17.x及以上未测试，不建议使用）
- yarn 需要 [1.22.x](https://yarnpkg.com/getting-started/install) 或以上版本

## 运行示例（测试/开发使用）

1. 配置 [.env](#.env配置文件說明) 配置项
2. 启动示例，执行命令 ```yarn run dev```

## 构建（生产用）

1. 配置 [.env](#.env配置文件說明) 配置项
2. vite 构建，执行命令 ```yarn run build```
