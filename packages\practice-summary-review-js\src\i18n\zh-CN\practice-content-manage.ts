export default {
  searchBar: {
    cultivationProjectPlace: '全部培养项目',
    practiceProgNoPlace: '全部课程号',
    keyword: '请输入学生学号、学生名称',
    userName: '学生名称',
    userNo: '学生学号',
    practiceProgNo: '课程号',
    cultivationProject: '培养项目名称',
    practiceUnit: '实践单位',
    practicePlace: '所在地点',
    onCampusSupervisorName: '校内导师',
    offCampusSupervisorName: '校外导师',
    reviewDocStatus: '全部成绩评定状态',
  },

  columns: {
    nameAndNo: '@:practiceApprove.columns.nameAndNo',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    projectInfo: '@:practiceApprove.columns.projectInfo',
    practiceUnit: '@:practiceApply.columns.practiceUnit',
    practicePlace: '@:practiceApply.columns.practicePlace',
    practiceStartDate: '专业实践开始日期',
    practiceEndDate: '专业实践结束日期',
    pendingCount: '待处理信息',
    evalResult: '评定成绩',
    evalResultStatus: '成绩评定状态',
  },

  label: {
    rejectReason: '驳回原因',
    passRemark: '意见',
  },

  placeholder: {
    rejectReason: '输入内容',
  },

  title: {
    practiceContentManage: '学生提交记录管理',
  },

  action: {
    view: '学生提交记录管理',
  },

  text: {
    pendingCount: '存在{num}条信息待处理',
  },
};
