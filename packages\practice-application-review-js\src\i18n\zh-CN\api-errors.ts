export default {
  common: {
    NO_EXISTS: '数据不存在',
    FREQUENT_OPERATIONS: '操作频繁，请您稍后再试',
  },

  export: {
    NO_EXISTS: '导出数据为空',
    EXPORT_DATA_LIMIT: '导出的数据记录已超出数量限制，本系统每次最多仅允许导出1048576条数据，请重新调整所需汇出数据的数量 ',
  },

  attachmentUpload: {
    ATTACHMENT_NAME_LENGTH: '附件名称长度超出30字符，请修改后再重新上传',
  },

  practiceApplyDraft: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  practiceApplySubmit: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    FREQUENT_OPERATIONS: '@:apiErrors.common.FREQUENT_OPERATIONS',
    LOCK_STATUS_ERROR: '@:apiErrors.practiceApplyUnlock.LOCK_STATUS_ERROR',
  },

  practiceApplyDetail: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  practiceApplyDelete: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    STATUS_ABNORMAL: '状态异常，“草稿”状态的申请单才支持删除',
  },

  practiceApplyUnlock: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    LOCK_STATUS_ERROR: '锁定状态有误，请稍后再试',
  },

  practiceApplyChange: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    FREQUENT_OPERATIONS: '@:apiErrors.common.FREQUENT_OPERATIONS',
    STATUS_ABNORMAL: '状态错误，“审核通过”状态的申请单，才支持发起申请修改',
    ALREADY_EXISTS: '申请单已存在一条待审核的异动申请',
    EXIST_ONGOING_PROCESSES: '该学员当前专业实践业务下，存在进行中的流程，无法进行操作！',
    PRACTICE_APPLY_UPDATE_FAIL_RECORD_STATUS_IS_ERROR: '该实践申请单下的实践过程记录存在“待校内导师审核”、“待校外导师审核”、“审核驳回”状态，不支持发起“申请修改”',
    PRACTICE_APPLY_UPDATE_FAIL_SUMMARY_STATUS_IS_ERROR: '该实践申请单下实践总结存在“待校内导师审核”、“待校外导师审核”、“审核驳回”状态，不支持发起“申请修改”',
    PRACTICE_APPLY_UPDATE_FAIL_REVIEW_DOC_STATUS_IS_ERROR: '该实践申请单下的审查材料，存在“审核驳回”、“待评定”状态，不支持发起“申请修改”',
    LOCK_STATUS_ERROR: '@:apiErrors.practiceApplyUnlock.LOCK_STATUS_ERROR',
    CHANGE_APPLY_EXIST_PENDING: '实践申请单或实践过程记录或实践总结，存在进行中的异动申请',
  },

  practiceApproveDetail: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  practiceApprove: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    STATUS_ABNORMAL: '状态异常',
    APPROVAL_STATUS_ERROR: '审批状态异常，请稍后再试',
    LOCK_STATUS_ERROR: '@:apiErrors.practiceApplyUnlock.LOCK_STATUS_ERROR',
  },

  changeApproveDetail: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  changeApprove: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    STATUS_ABNORMAL: '状态错误，“待审核”状态的审核单，才支持审核',
    APPROVAL_STATUS_ERROR: '@:apiErrors.practiceApprove.APPROVAL_STATUS_ERROR',
    APPROVAL_NODE_NOT_EXIST: '审批节点不存在',
    APPROVAL_NODE_USER_NOT_EXIST: '审批节点对应审批人为空',
  },

  processSubmit: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    PRACTICE_RECORD_STATUS_ERROR: '实践过程状态有误，请稍后再试',
    PRACTICE_APPLY_REC_CHANGE_NO_PASS_OR_REJECT: '实践申请单的异动审核没有审核通过或者审核驳回',
    PRACTICE_RECORD_SUBMIT_FAIL_EXIST_ONGOING: '您当前已存在一条进行中的实践过程记录申请，请等当前实践过程记录申请通过之后，再进行发起，建议先“暂存草稿”',
    PRACTICE_RECORD_HAS_EXIST_DRAFT: '@:myPractice.process.hint.draftDataExists',
    PRACTICE_RECORD_EXIST_PENDING_CHANGE_APPLY: '当前实践过程记录已存在一条正在审核中的异动申请',
  },

  processApprove: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    APPROVAL_STATUS_ERROR: '审批状态异常，请稍后再试',
  },

  conclusionSubmit: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    PRACTICE_SUMMARY_STATUS_ERROR: '实践总结状态有误，请稍后再试',
    PRACTICE_APPLY_REC_CHANGE_NO_PASS_OR_REJECT: '实践申请单的异动审核没有审核通过或者审核驳回',
    PRACTICE_SUMMARY_SUBMIT_FAIL_RECORD_STATUS_IS_ERROR: '只有实践过程记录全部提交通过之后，才能进行实践总结的提交',
    PRACTICE_SUMMARY_HAS_EXIST: '该实践申请单下的实践总结已存在，请稍后再试',
    PRACTICE_SUMMARY_EXIST_PENDING_CHANGE_APPLY: '当前实践总结已存在一条正在审核中的异动申请',
  },

  reviewMaterial: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    REVIEW_DOC_STATUS_ERROR: '审查材料状态有误，请稍后再试',
    REVIEW_DOC_HAS_EXIST: '该实践申请单下的审查材料已存在，请稍后再试',
    PRACTICE_APPLY_REC_CHANGE_NO_PASS_OR_REJECT: '实践申请单的异动审核没有审核通过或者审核驳回',
    REVIEW_DOC_SUBMIT_FAIL_SUMMARY_STATUS_IS_ERROR: '只有实践总结提交通过之后，才能进行审查材料的提交',
  },

  feedback: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    FEEDBACK_STATUS_ERROR: '反馈状态有误，请稍后再试',
  },

  systemManageRoles: {
    USER_ID_INVALID_FIELD: '登录过期，请重新登录',
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    ALREADY_EXISTS: '角色编码已存在',
    BUSY: '该角色已分配给具体用户，如需删除，请先移除角色底下的用户',
  },
};
