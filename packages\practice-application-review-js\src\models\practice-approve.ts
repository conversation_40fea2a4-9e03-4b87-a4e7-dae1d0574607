import type { PracticeApproveDetailType } from '@/types/practice-approve';
import { createPracticeDetailModel as baseModel } from '@/models/practice';

export function createPracticeDetailModel(): PracticeApproveDetailType {
  return {
    id: null,
    status: null,
    canApprove: null,
    changePracticeApplyRecDetails: baseModel(),
  };
}

export function createModel(): ApproveFormType {
  return {
    remark: '',
  };
}

export interface ApproveFormType {
  remark: string;
}
