# 服务运行端口
# 必填
VITE_PORT=8080

# 服务 URL
# 选填
# 不填写默认为localhost地址
# 注：URL 不包含 PUBLIC_PATH
VITE_SERVICE_URL=http://localhost:8080

# 根目录
# 必填
VITE_PUBLIC_PATH=/practice-application-review-js/

# 服务编码
# 必填
VITE_SERVICE_CODE=tsinghua-engineering-practice-teacher

# 专业实践管理端-服务编码
# 必填
VITE_ENGINEERING_PRACTICE_SERVICE_CODE=tsinghua-engineering-practice

### 接口相关 - 起始 ###
# 专业实践管理接口 URL
# 必填
VITE_ENGINEERING_PRACTICE_API_BASE_URL=https://tsinghuadev.doocom.cn/engineering-practice_api

# 统一应用 API URL
# 必填
VITE_BDC_SERVICE_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-service_api

# 静态资源 URL
# 必填
VITE_STATIC_RESOURCES_BASE_URL=https://tsinghuadev.doocom.cn/static-resources
### 接口相关 - 結束 ###
