import _ from 'lodash';

import type {
  PracticeApplyBaseInfoType,
  PracticeApplyTuitionMemberType,
  PracticeApplyContentType,
  PracticeDetailApiType,
  PracticeDetailType,
} from '@/types/practice';

import {
  createPracticeApplyBaseInfoModel,
  createPracticeApplyTuitionMemberModel,
  createPracticeApplyContentModel,
} from '@/models/practice';
import { dateConvert } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { SupervisorType } from '@/consts/supervisor-type';
import { createSupervisorsModel } from '@/models/supervisors';


export function handlePracticeDetailData(data: PracticeDetailApiType): PracticeDetailType {
  const td = namespaceT('dateFormat');

  const baseInfoKeys = Object.keys(createPracticeApplyBaseInfoModel());
  const baseInfo = _.pick(data, baseInfoKeys) as PracticeApplyBaseInfoType;

  const tuitionMemberKeys = Object.keys(createPracticeApplyTuitionMemberModel());
  const tuitionMember = _.pick(data, tuitionMemberKeys) as PracticeApplyTuitionMemberType;

  const contentKeys = Object.keys(createPracticeApplyContentModel());
  const content = _.pick(data, contentKeys) as PracticeApplyContentType;

  content.practiceDate = {
    practiceStartDate: dateConvert(data.practiceStartDate, td('date')),
    practiceEndDate: dateConvert(data.practiceEndDate, td('date')),
  };

  const campusSupervisorList = (data.campusSupervisorList || []).map((item) => ({
    userId: item.supervisorUserId,
    userName: item.supervisorName,
    userNo: item.supervisorUserNo,
    jobTitle: item.jobTitle,
    researchDomain: item.researchDomain,
    workUnit: item.workUnit,
    mobile: item.phone,
    email: item.email,
    supervisorType: item.supervisorType,
  }));

  tuitionMember.offCampusSupervisor = campusSupervisorList
    .find((item) => item.supervisorType === SupervisorType.OFF_CAMPUS) || createSupervisorsModel();
  tuitionMember.onCampusSupervisor = campusSupervisorList
    .find((item) => item.supervisorType === SupervisorType.ON_CAMPUS) || createSupervisorsModel();
  tuitionMember.onCampusSupervisorUserId = tuitionMember.onCampusSupervisor?.userId;
  tuitionMember.offCampusSupervisorUserId = tuitionMember.offCampusSupervisor?.userId;

  return {
    baseInfo,
    tuitionMember,
    content,
  };
}
