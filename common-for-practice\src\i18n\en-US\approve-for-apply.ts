export default {
  navTitle: {
    baseInfo: '学生基本信息',
    tuitionMember: '指导小组成员',
    practiceContent: '内容及计划',
  },

  subTitle: {
    schoolTutor: '校内导师',
    externalTutor: '校外导师',
    practiceUnit: '实践单位',
    progFinishInfo: '培养计划中课程部分完成情况',
    practiceOpenTime: '申请开展专业实践起止时间',
    practiceContent: '实践计划与内容',
    practiceProgress: '实践进度',
  },

  label:{
    name: '姓名',
    userNo: '学号',
    mobile: '手机号', // TODO 新翻译
    email: '电子邮箱', // TODO 新翻译
    freshGraduateInd: '入学前是否是应届生',
    cultivationProject: '专业工程领域',
    researchDirection: '研究方向',
    supervisor: '导师',
    practiceProgNo: '专业实践课程号',
    applyTime: '填表日期',


    unitName: '单位名称', // TODO 新翻译
    practiceUnitType: '企业类型',
    unitAddress:'单位地址',// TODO 新翻译
    detailAddress: '详细地址',
    practiceUnitInCharge: '联系人',
    call: '称呼',
    
    modifyContent: '修改内容说明',
  },

  columns:{
    onCampusTutor: '校内导师',
    offCampusTutor: '校外导师',
  }
}