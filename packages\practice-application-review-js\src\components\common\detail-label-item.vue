<script lang="ts" setup>
withDefaults(defineProps<{
  label?: string;
  labelWidth?: number;
  columnFlex?: boolean;
}>(), {
  label: '',
  labelWidth: 100,
  columnFlex: false,
});
</script>


<template>
  <div
    class="detail-pair-label-item"
    :class="{'column-flex': columnFlex}"
  >
    <div
      v-if="label"
      class="label"
      :style="{'width': `${labelWidth}px`}"
      v-text="label"
    />
    <div
      class="content"
    >
      <slot>--</slot>
    </div>
  </div>
</template>


<style lang="less" scoped>
.detail-pair-label-item {
  display: flex;
  gap: 22px;
  align-items: flex-start;
  font-size: 15px;
  line-height: 23px;

  &:not(:last-child) {
    margin-bottom: 10px;
  }

  &.column-flex {
    flex-direction: column;
    gap: 12px;
  }

  .label {
    width: 100px;
    color: #8d8e8f;
    font-weight: 400;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
  }

  .content {
    flex: 1;
    max-width: calc(100% - 122px);
    color: #3b3c3d;
    font-weight: 400;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    overflow: auto;
  }
}
</style>
