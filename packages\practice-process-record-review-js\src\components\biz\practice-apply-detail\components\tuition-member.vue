<script setup lang="ts">
import { computed } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import type { PracticeApplyTuitionMemberType } from '@/types/practice';

import CellTitle from '@/components/biz/cell-title.vue';
import TuitionMemberItem from './tuition-member-item.vue';

const props = withDefaults(defineProps<{
  tuitionMember: PracticeApplyTuitionMemberType
}>(), {});


const t = namespaceT('practiceApply.form');

const onCampusSupervisor = computed(() => {
  return props.tuitionMember.onCampusSupervisor || {};
});

const offCampusSupervisor = computed(() => {
  return props.tuitionMember.offCampusSupervisor || {};
});
</script>


<template>
  <div class="pl-20">
    <CellTitle
      :title="t('subTitle.schoolTutor')"
    />
    <TuitionMemberItem :model="onCampusSupervisor" />

    <CellTitle
      :title="t('subTitle.externalTutor')"
      class="mt-20"
    />
    <TuitionMemberItem :model="offCampusSupervisor" />
  </div>
</template>
