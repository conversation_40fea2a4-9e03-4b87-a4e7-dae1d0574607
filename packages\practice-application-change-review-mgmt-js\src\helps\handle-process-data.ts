import _ from 'lodash';

import type { PracticeProcessModelType } from '@/types/practice-process';
import { dateConvert, dateFormat } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';

export function handleProcessDetailData(data: PracticeProcessModelType): PracticeProcessModelType {
  const td = namespaceT('dateFormat');
  const res = { ...data };

  if (data.startTime) {
    res.startTime = dateConvert(data.startTime, td('date'));
  }

  if (data.endTime) {
    res.endTime = dateConvert(data.endTime, td('date'));
  }

  res.attachmentList = data.attachmentList || [];
  res.attachmentIdList = (data.attachmentList || []).map((i) => i.id);

  res.content = data.content || '';

  return res;
}


const pickKeys = [
  'projectName',
  'startTime',
  'endTime',
  'content',
  'attachmentIdList',
  'modifyContent',
] as const;

type PickPostType = typeof pickKeys[number];
export function handleProcessPostData(data: PracticeProcessModelType): Pick<PracticeProcessModelType, PickPostType> {
  const td = namespaceT('dateFormat');
  const res = { ...data };

  if (data.startTime) {
    res.startTime = dateFormat(data.startTime, td('date'));
  }
  if (data.endTime) {
    res.endTime = dateFormat(data.endTime, td('date'));
  }

  return _.pick(res, pickKeys);
}
