import _ from 'lodash';
import { LOCAL_BDC_SERVICE_API_BASE_URL } from '@/config/api';
import { CommonApi } from '@/api/common/common-api';
import { valueByLocale } from '@/helps/locale';
import type { DictType } from '@/types/dict';

export class DataTagsApi extends CommonApi<DictType[]> {
  typeCode: string;

  serviceCode: string;

  constructor({ typeCode, serviceCode = import.meta.env.VITE_SERVICE_CODE }) {
    super({
      baseURL: LOCAL_BDC_SERVICE_API_BASE_URL,
    });
    this.typeCode = typeCode;
    this.serviceCode = serviceCode;
  }

  url() {
    // eslint-disable-next-line max-len
    return `/services/${this.serviceCode}/service-dict-type/${this.typeCode}/service-dicts`;
  }

  async send(): Promise<DictType[]> {
    const res = await super.send();
    let data: Array<DictType> = _.get(res, 'model', []);
    data = (data || []).map((item) => {
      return {
        ...item,
        nameByLocale: valueByLocale(item.name, item.enName),
      };
    });

    return data;
  }
}
