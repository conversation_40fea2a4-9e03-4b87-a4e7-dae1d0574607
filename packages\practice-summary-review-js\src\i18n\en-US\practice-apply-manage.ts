export default {
  searchBar: {
    statusPlace: '@:practiceApply.searchBar.statusPlace',
    lockStatusPlace: '@:practiceApply.searchBar.lockStatusPlace',
    keyword: 'Please enter student ID or name',
    userName: 'Student name',
    userNo: 'Student ID',
    practiceProgNo: '@:practiceApply.searchBar.practiceProgNo',
    cultivationProject: '@:practiceApply.searchBar.cultivationProject',
    practiceUnit: '@:practiceApply.searchBar.practiceUnit',
    practicePlace: '@:practiceApply.searchBar.practicePlace',
    status: '@:practiceApply.searchBar.status',
    lockStatus: '@:practiceApply.searchBar.lockStatus',
    applyTime: '@:practiceApply.searchBar.applyTime',
  },

  columns: {
    nameAndNo: '@:practiceApply.columns.nameAndNo',
    projectInfo: 'Training program / Course code',
    status: '@:practiceApply.columns.status',
    practiceTime: 'Start / End time',
    practiceWeeks: 'Total weeks',
    practiceUnitInfo: 'Institution where you practice/ Location',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    lockStatus: '@:practiceApply.columns.lockStatus',
    creTime: '@:practiceApply.columns.creTime',
    updInfo: 'Last operated by / Time',
  },

  title: {
    practiceApply: 'Add new practice application',
    practiceApplyEdit: 'Modify application content',
  },

  action: {
    view: '@:common.action.view',
    delete: '@:common.action.delete',
    save: '@:common.action.save',
    lock: 'Lock',
    unlock: 'Unlock',
    edit: 'Modify application content',
    editOnCampusTutor: 'Modify on-campus supervisor',
    editOffCampusTutor: 'Modify off-campus supervisor',
    createPracticeApply: 'Add new practice application',
  },

  hint: {
    lockTitle: 'Lock',
    lockConfirm: 'Confirm lock?',
    lockSuccess: 'Locked successfully',
    delTitle: '@:practiceApply.hint.delTitle',
    delConfirm: '@:practiceApply.hint.delConfirm',
    delSuccess: '@:practiceApply.hint.delSuccess',
    unlockTitle: 'Unlock',
    unlockConfirm: 'Confirm unlock?',
    unlockSuccess: 'Unlock successfully',
    saveSucc: 'Saved successfully',
    // eslint-disable-next-line max-len
    editValidFail: 'The student has an ongoing process under the current professional practice, and no further actions can be performed',
  },
};
