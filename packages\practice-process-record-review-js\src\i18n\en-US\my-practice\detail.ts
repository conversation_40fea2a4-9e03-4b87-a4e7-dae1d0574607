export default {
  label: {
    userNo: 'Student ID：',
    practiceUnit: 'The institution where you practiced',
    practicePlace: 'Practice location',
    onCampusTutor: 'On-campus supervisor',
    offCampusTutor: 'Off-campus supervisor',
    practiceTime: 'Professional practice duration',
    currentStatus: 'Current status',
    headway: 'Progress',
  },

  currentStatus: [
    'Practice application submitted',
    'Practice process record submitted',
    'Practice summary submitted',
    'Review materials submitted',
  ],

  tabs: {
    practiceApplication: 'Practice application form',
    practiceApplicationModifyRecord: 'Practice application form modification record',
    practiceProcessRecord: 'Practice process record',
    practiceProcessModifyRecord: 'Practice process modification record',
    practiceConsume: 'Practice summary',
    practiceConsumeModifyRecord: 'Practice summary modification record',
    reviewMaterials: 'Review materials',
  },
};
