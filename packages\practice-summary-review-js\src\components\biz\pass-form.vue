<script lang="ts" setup>
import { ref } from 'vue';

import PimaInput from '@/components/common/pima-input.vue';

import { InputType } from '@/consts/input-type';
import { namespaceT } from '@/helps/namespace-t';
import { formScrollIntoError } from '@/utils/form-scroll-into-error';
import { createInputRules } from '@/helps/rules';

import type { ApproveFormType } from '@/types/approve-form-type';


const rules = {
  remark: [
    createInputRules(),
  ],
};

const t = namespaceT('practiceContentManage');
const model = defineModel<ApproveFormType>();
const formRef = ref();


async function validate() {
  const valid = await formRef.value.validate();
  if (!valid) {
    formScrollIntoError(formRef.value);
    return false;
  }
  return true;
}

function resetFields() {
  return formRef.value.resetFields();
}

defineExpose({
  validate,
  resetFields,
});
</script>


<template>
  <Form
    ref="formRef"
    class="pima-form"
    :model="model"
    :rules="rules"
  >
    <FormItem
      prop="remark"
      :label="t('label.passRemark')"
    >
      <PimaInput
        v-model.trim="model.remark"
        :maxlength="300"
        :placeholder="t('placeholder.rejectReason')"
        :type="InputType.TEXTARER"
        show-word-limit
        :rows="5"
      />
    </FormItem>
  </Form>
</template>
