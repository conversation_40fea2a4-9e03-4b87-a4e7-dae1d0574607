import type { ChangeApproveDetailType } from '@/types/practice-change-approve';
import { createPracticeDetailModel } from '@/models/practice';
import { createPracticeProcessModel } from '@/models/practice-process';
import { createPracticeConclusionModel } from '@/models/practice-conclusion';

export function createChangeApproveDetail(): ChangeApproveDetailType {
  return {
    id: null,
    canApprove: null,
    relateType: null,
    changePracticeApplyRecDetails: createPracticeDetailModel(),
    changePracticeRecordDetails: createPracticeProcessModel(),
    changePracticeSummaryDetails: createPracticeConclusionModel(),
  };
}
