import _ from 'lodash';
import type { PracticeConclusionModelType } from '@/types/practice-conclusion';


const pickKeys = [
  'practiceApplyRecId',
  'practiceTitle',
  'tutor',
  'attachmentIdList',
  'practiceAim',
  'practiceContent',
  'practiceAchievement',
  'modifyContent',
] as const;

type PickPostType = typeof pickKeys[number];
export function handleConclusionPostData(data: PracticeConclusionModelType)
  : Pick<PracticeConclusionModelType, PickPostType> {
  const res = { ...data };

  return _.pick(res, pickKeys);
}


export function handleConclusionDetailData(data: PracticeConclusionModelType): PracticeConclusionModelType {
  const res = { ...data };

  res.attachmentList = data.attachmentList || [];
  res.attachmentIdList = res.attachmentList.map((i) => i.id);

  return res;
}
