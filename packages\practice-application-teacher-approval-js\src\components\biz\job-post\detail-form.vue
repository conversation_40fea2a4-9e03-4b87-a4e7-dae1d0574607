<script setup lang="ts">
import { computed, nextTick, onBeforeMount } from 'vue';

import DetailLabelItem from '@/components/common/detail-label-item.vue';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';

import type { JobPostDetailType } from '@/types/double-selection';

import { namespaceT } from '@/helps/namespace-t';
import { formateToDate } from '@/helps/formate-to-date-type';
import { useSexStore } from '@/store/data-tags/sex';
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { useNamedStore } from '@/store/data-tags/named';

interface Props {
  isMobile: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false,
});


const model = defineModel<JobPostDetailType>();

const t = namespaceT('doubleSelection');
const postTypeStore = usePostTypeStore();
const sexStore = useSexStore();
const titleStore = useNamedStore();

/** 实践地址 */
const practiceAddress = computed(() => {
  const { practicePlaceAddrProvName, practicePlaceAddrCityName, practicePlaceAddrAreaName } = model.value;
  let addr = '';
  if (practicePlaceAddrProvName) {
    addr += practicePlaceAddrProvName;
  }
  if (practicePlaceAddrCityName) {
    addr += practicePlaceAddrCityName;
  }
  if (practicePlaceAddrAreaName) {
    addr += practicePlaceAddrAreaName;
  }

  return addr;
});

/** span:halfFullSpan 移动端需占满一行 */
const halfFullSpan = computed(() => {
  return props.isMobile ? 24 : 12;
});


onBeforeMount(async () => {
  await nextTick();
  postTypeStore.loadDataIfNeeded();
  sexStore.loadDataIfNeeded();
  titleStore.loadDataIfNeeded();
});


</script>


<template>
  <Row
    :gutter="24"
    class="pl-20"
  >
    <!-- 岗位名称 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.postName')"
        :value="model.jobTitle"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 岗位类型 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.postType')"
        :value="postTypeStore.getTextByCode(model.jobType)"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 研究方向 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.researchDirection')"
        :value="model.researchField"
        column-flex
      />
    </Col>

    <!-- 性别要求 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.sexRemand')"
        :value="sexStore.getTextByCode(model.sex)"
        :column-flex="isMobile"
      />
    </Col>


    <!-- 所需专业 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.neededMajor')"
        :value="model.majorReq"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 需求人数 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.numOfPerson')"
        :value="model.recruitAmt"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 工作内容 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.jobContent')"
        column-flex
      >
        <PimaSanitizeHtml :inner-html="model.jobContent" />
      </DetailLabelItem>
    </Col>

    <!-- 报名截止时间 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.deadline')"
        :value="formateToDate(model.endTime)"
        column-flex
      />
    </Col>

    <!-- 其他要求 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.otherRemand')"
        :value="model.othReq"
        column-flex
      />
    </Col>

    <!-- 岗位单位 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.postUnit')"
        :value="model.baseName"
        column-flex
      />
    </Col>

    <!-- 联系人 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.contactor')"
        :value="model.baseContact"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 称呼 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.name')"
        :value="titleStore.getTextByCode(model.contactTitle)"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 手机号 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.phone')"
        :value="model.mobile"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 电子邮箱 -->
    <Col :span="halfFullSpan">
      <DetailLabelItem
        :label="t('label.email')"
        :value="model.email"
        :column-flex="isMobile"
      />
    </Col>

    <!-- 实践地址 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.practiceAddress')"
        :value="practiceAddress"
        column-flex
      />
    </Col>

    <!-- 详细地址 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.detailedAddress')"
        :value="model.practicePlace"
        column-flex
      />
    </Col>

    <!-- 公司简介 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.companyProfile')"
        :value="model.baseSummary"
        column-flex
      />
    </Col>
  </Row>
</template>
