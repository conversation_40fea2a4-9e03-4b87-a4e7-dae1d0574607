import type { InternalAxiosRequestConfig } from 'axios';
import _ from 'lodash';
import { md5 } from 'js-md5';


function isFormData(thing) {
  return thing instanceof FormData;
}

function pairsQuery(query) {
  const ps = _.toPairs(query).map(([k, v]) => {
    if (_.isArray(v)) {
      return [k, (_.isEmpty(v) ? null : v[0])];
    }

    return [k, v];
  });

  return ps;
}

// 处理Body为正常的JSON键值对
function pairsBodyGeneral(body) {
  if (isFormData(body)) {
    return [];
  }

  if (_.isArray(body)) {
    return [];
  }

  const ps = _.toPairs(body).map(([k, v]) => {
    if (_.isObject(v)) {
      return [k, JSON.stringify(v)];
    }

    return [k, v];
  });

  return ps;
}

// 处理Body为JSON数组格式
function pairsBodyArray(body) {
  if (isFormData(body)) {
    return [];
  }

  if (!_.isArray(body)) {
    return [];
  }

  let pairs = [];
  _.forEach(body, (it) => {
    const c = _.map(_.keys(it).sort(), (k) => {
      const v = it[k];
      if (_.isObject(v)) {
        return [k, JSON.stringify(v)];
      }

      return [k, v];
    });

    pairs = pairs.concat(c);
  });

  return pairs;
}

function signatureString(pairs, sort = true) {
  const process = _.flow(
    (pairs_) => {
      return _.filter(pairs_, ([, v]) => {
        return !_.isNil(v);
      });
    },

    (pairs_) => {
      if (sort) {
        return _.sortBy(pairs_, ([k]) => k);
      }

      return pairs;
    },

    (pairs_) => {
      return pairs_.map(([k, v]) => `${k}=${v}`).join('');
    },
  );

  return process(pairs);
}

function signature(salt, query, body) {
  const encrypted = (salt_, raw) => {
    return md5(`${raw}${salt_}`);
  };

  const pq = pairsQuery(query);
  const pbg = pairsBodyGeneral(body);
  const pba = pairsBodyArray(body);
  const s1 = signatureString(pq.concat(pbg));
  const s2 = signatureString(pba, false);
  const es = encrypted(salt, `${s1}${s2}`);
  return es;
}

type SignatureInterceptorOptions = {
  salt: string;
};

export function interceptorSignature({ salt }: SignatureInterceptorOptions)
  : (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig {
  return (config: InternalAxiosRequestConfig) => {
    const s = signature(
      salt,
      config.params,
      config.data,
    );

    Object.assign(config.params, {
      signature: s,
    });

    return config;
  };
}
