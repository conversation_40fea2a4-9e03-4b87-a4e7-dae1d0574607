module.exports = {
  extends: '../.eslintrc',
	parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 6,
    sourceType: 'module',
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
    createDefaultProgram: true,
    extraFileExtensions: ['.vue'],
  },
  settings: {
		'import/resolver': {
			'typescript': {
				'alwaysTryTypes': true,
			},
			alias: [
				['^', './src'],
			],
		},
	},
  globals: {
    NavItem: 'readonly',
    NavigationBar: 'readonly',
  },
};
