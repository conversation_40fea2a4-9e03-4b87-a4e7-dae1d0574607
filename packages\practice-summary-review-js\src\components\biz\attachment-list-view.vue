<script setup lang="ts">
import URI from 'urijs';

import type { AttachmentVO } from '@/types/attachment';

const props = withDefaults(defineProps<{
  attachments: Array<AttachmentVO> | null;
  isPreview?: boolean;
}>(), {
  isPreview: false,
});

function handleAttachUrl(url: string) {
  if (props.isPreview) {
    const uri = new URI(url);
    if (uri.hasQuery('filename')) {
      uri.removeQuery('filename');
    }
    return uri.toString();
  }
  return url;
}
</script>


<template>
  <span v-if="!attachments?.length">--</span>
  <a
    v-for="file in attachments"
    :key="`file-${file.id}`"
    class="file-item"
    :href="handleAttachUrl(file.filePath.normal)"
    target="_blank"
  >
    <div class="file-title-box">
      <img
        src="@/assets/img/icon-attachment.svg"
        class="icon-attach"
      >
      <span class="file-title">
        {{ file.origFileName }}
      </span>
    </div>
    <img
      v-if="isPreview"
      src="@/assets/img/icon-preview.svg"
      class="icon-download"
    >
    <img
      v-else
      src="@/assets/img/icon-download.svg"
      class="icon-download"
    >
  </a>
</template>


<style lang="less" scoped>
.file-item {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
  margin-bottom: 12px;
  padding: 7px 12px;
  word-break: break-word;
  background-color: #f5f5f5;
  border-radius: 3px;

  .file-title-box {
    display: flex;
    flex: 1;
    gap: 8px;
    align-items: center;
  }

  .file-title {
    flex: 1;
    overflow: hidden;
    color: #111;
    font-weight: 400;
    font-size: 13px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &:hover .file-title {
    color: var(--primary-color);
  }
}
</style>
