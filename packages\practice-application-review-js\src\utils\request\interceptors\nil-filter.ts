import type { InternalAxiosRequestConfig } from 'axios';
import _ from 'lodash';

/**
 * 过滤数组中的nil值（null或undefined）。
 *
 * @param arr - 待过滤的数组。
 * @returns 过滤后的数组，不包含nil值。
 */
function arrayNilFilter(arr: Array<unknown>): Array<unknown> {
  return arr.filter((o) => !_.isNil(o));
}

/**
 * 从对象中过滤掉值为nil（null或undefined）的键。
 *
 * @param {Object} obj - 待过滤的对象。
 * @returns {Object} 过滤后的对象，不包含值为nil的键。
 */
function objectNilFilter(obj: { [key: string]: unknown }): { [key: string]: unknown } {
  return _.pick(obj, Object.keys(obj).filter((key) => !_.isNil(obj[key])));
}

/**
 * 递归过滤数组或对象中的nil值。
 *
 * @param target - 可能为数组、对象或其他类型的值。
 * @returns 过滤后的值，如果是数组或对象，则去除了nil值。
 */
function nilFilter(target: unknown): unknown {
  if (_.isArray(target)) {
    const filteredArray = arrayNilFilter(target);
    return filteredArray.map((item) => nilFilter(item));
  }

  if (_.isPlainObject(target)) {
    const filteredObj = objectNilFilter(target as { [key: string]: unknown });
    Object.keys(filteredObj).forEach((key) => {
      filteredObj[key] = nilFilter(filteredObj[key]);
    });
    return filteredObj;
  }

  return target;
}

/**
 * 拦截器函数，用于过滤请求配置中的nil值，包括params和data。
 *
 * @param {Object} config - 请求的配置对象。
 * @returns {Object} 更新后的配置对象，其中nil值已被过滤。
 */
export function interceptorNilFilter(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig {
  const params = objectNilFilter(config.params || {});
  Object.assign(config, {
    params,
  });


  if (config.data instanceof FormData) {
    return config;
  }

  const data = nilFilter(_.cloneDeep(config.data));
  Object.assign(config, {
    data,
  });

  return config;
}
