export interface SearchModelType {
  userName: string;
  userNo: string;
  practiceProgNo: string;
  practiceUnit: string;
  practicePlace: string;
  cultivationProject: string;
  status: string;
  lockStatus: string;
  applyTimeStart: string;
  applyTimeEnd: string;
  keyword: string;
}

export interface PracticeApproveListType {
  id: number;
  userName: string;
  userNo: string;
  onCampusSupervisorName: string;
  offCampusSupervisorName: string;
  practiceStartDate: string;
  practiceEndDate: string;
  practiceWeeks: number;
  practiceProgNo: string;
  practiceUnit: string;
  practicePlace: string;
  cultivationProject: string;
  status: string;
  lockStatus: string;
  applyTime: string;
  canApprove: boolean;
  canUnLock: boolean;
  updName: string;
  updTime: string;
}

export interface PracticeApplyApiDetailType {
  id: number;
  status: string;
  canApprove: boolean;
  practiceApplyRecDetailsVo: PracticeDetailApiType;
}

export interface PracticeApproveDetailType {
  id: number;
  status: string;
  canApprove: boolean;
  changePracticeApplyRecDetails: PracticeDetailApiType;
}
