<script lang="ts" setup>
import { ref, reactive, watch, toRef } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { PracticeApproveRejectApi } from '@/api/practice-approve/approve-reject';

import PimaModal from '@/components/common/pima-modal.vue';
import RejectForm from './reject-form.vue';

import { createModel } from '@/models/practice-approve';


const props = withDefaults(defineProps<{
  modelValue: boolean;
  detailId: number | string | null;
}>(), {});

interface EmitType {
  (e: 'update:modelValue', value: boolean): void
  (e: 'on-success'): void
}


const emit = defineEmits<EmitType>();

const t = namespaceT('practiceApprove');
const tm = namespaceT('common');
const shown = toRef(props, 'modelValue');
const formRef = ref();

const saving = ref(false);
const model = reactive(createModel());

function onClose() {
  emit('update:modelValue', false);
}

async function onConfirm() {
  await formRef.value.onSave();
}

async function onSave() {
  try {
    saving.value = true;
    const api = new PracticeApproveRejectApi({ id: props.detailId });
    api.data = {
      ...model,
    };

    await api.send();
    openToastSuccess(t('hint.saveSucc'));
    emit('on-success');
    onClose();
  } catch (error) {
    openToastError(error.message);

    throw error;
  } finally {
    saving.value = false;
  }
}

watch(shown, (val) => {
  if (val) {
    Object.assign(model, createModel());
    formRef.value.resetFields();
  }
});
</script>


<template>
  <PimaModal
    :title="t('title.reject')"
    :value="modelValue"
    :loading="saving"
    :confirm-text="tm('action.ok')"
    v-bind="$attrs"
    @confirm="onConfirm"
    @cancel="onClose"
  >
    <div class="block">
      <RejectForm
        ref="formRef"
        v-model="model"
        @on-save="onSave()"
      />
    </div>
  </PimaModal>
</template>
