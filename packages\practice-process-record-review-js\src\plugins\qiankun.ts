import { renderWithQiankun } from 'vite-plugin-qiankun/es/helper';
import eventBus from '@/utils/event-bus';
import { useQiankunStore } from '@/store/qiankun';
import {
  injectEngineeringThemeIViewCss,
  removeEngineeringThemeIViewCss,
} from '@/helps/inject';
import { i18n } from '@/i18n';
import { FALLBACK_LOCALE } from '@/config/locale';

/**
 * @description 注册乾坤
 * @param props
 */
export const registerQiankun = (lifeCycle: {
  render: (container?: HTMLElement | string) => void;
  bootstrap?: () => void;
  update?: () => void;
  unmount?: () => void;
}) => {
  const { render, unmount } = lifeCycle;
  renderWithQiankun({
    bootstrap() {
      // eslint-disable-next-line no-console
      console.log('[Microservice] bootstrap');
    },

    mount(props) {
      // eslint-disable-next-line no-console
      console.log('[Microservice] mount');

      render(props.container);

      const injectState = props.state ?? {};
      const { locale } = props.state;

      const qiankunStore = useQiankunStore();

      i18n.global.locale.value = locale ?? FALLBACK_LOCALE;

      if (props.channel !== import.meta.env.VITE_SERVICE_CODE) {
        injectEngineeringThemeIViewCss();
      }

      qiankunStore.setState({
        code: props.name,
        dataId: props.dataId,
        setGlobalState: props.setGlobalState,
        token: props.token,
        isApprove: props.isApprove,
        isToDo: props.channel !== import.meta.env.VITE_SERVICE_CODE,
        onSuccess: props.onSuccess,
        onCancel: props.onCancel,
        goBack: props.goBack,
        ...injectState,
      });

      props.onGlobalStateChange((state: Record<string, unknown>) => {
        eventBus.emit('onGlobalStateChange', state);
      }, true);
    },

    update(props) {
      // eslint-disable-next-line no-console
      console.log('[Microservice] update', props);
    },

    unmount() {
      // eslint-disable-next-line no-console
      console.log('[Microservice] unmount');

      const qiankunStore = useQiankunStore();
      qiankunStore.resetGlobalState();
      eventBus.off('onGlobalStateChange');
      removeEngineeringThemeIViewCss();
      unmount?.();
    },
  });
};
