export default {
  action: {
    view: '@:myPractice.applyMod.action.view',
    add: '点击上传审查材料',
    cancel: '@:common.action.cancel',
    draft: '@:myPractice.process.action.draft',
    submit: '@:myPractice.process.action.submit',
    save: '@:myPractice.process.action.save',
    modify: '@:myPractice.process.action.modify',
    review: '评定',
    previewOnline: '在线预览',
    reject: '审核驳回',
    pass: '确认评定',
  },

  title: {
    add: '添加审查材料',
    reviewMaterials: '审查材料',
    edit: '@:myPractice.process.title.edit',
    view: '@:myPractice.process.title.view',
    restart: '@:myPractice.process.title.restart',
    changeApply: '@:myPractice.process.title.changeApply',
    actionLogTitle: '审查材料流转情况',
    review: '成绩评定',
  },

  hint: {
    saveDraftSucc: '@:myPractice.process.hint.saveDraftSucc',
    submitSucc: '@:myPractice.process.hint.submitSucc',
    delSuccess: '@:myPractice.process.hint.delSuccess',
    delTitle: '@:myPractice.process.hint.delTitle',
    delConfirm: '@:myPractice.process.hint.delConfirm',
    saveSucc: '@:myPractice.process.hint.saveSucc',
    passTitle: '确认评定',
    passConfirm: '是否确认评定吗？',
    approveSuccess: '@:myPractice.process.hint.approveSuccess',
  },

  label: {
    userNo: '@:myPractice.process.label.userNo',
    userName: '@:myPractice.process.label.userName',
    projectName: '@:myPractice.process.label.projectName',
    practiceUnit: '@:myPractice.conclusion.label.practiceUnit',
    cultivationProject: '@:practiceApply.form.label.cultivationProject',
    supervisor: '@:myPractice.conclusion.label.supervisor',
    practiceTime: '实践起止时间',
    practiceProgNo: '@:practiceApply.form.label.practiceProgNo',
    summaryReportAttachmentList: '总结报告',
    practiceProvenAttachmentList: '实践证明',
  },

  text: {
    attachmentTip: '请上传PDF格式的文件，文件不大于20MB',
  },

  evalResult: {
    y: '合格',
    n: '不合格',
  },
};
