import form from './form';
import print from './print';

export default {
  searchBar: {
    status: '状态',
    statusPlace: '全部状态',
    lockStatus: '锁定状态',
    lockStatusPlace: '全部锁定状态',
    keyword: '请输入实践单位名称，多个检索词用"\\"隔开',
    practiceProgNo: '课程号',
    practiceUnit: '实践单位',
    practicePlace: '所在地点',
    practicePlacePlace: '多个检索词用"\\"隔开',
    cultivationProject: '培养项目名称',
    applyTime: '申请时间',
    tutorName: '请输入姓名',
    tutorMobile: '请输入联系电话',
  },

  title: {
    practiceApply: '发起实践申请',
    applyStatement: '专业实践申请知情同意书',
    practiceApplyDetail: '实践申请详情',
    practiceApplyEdit: '@:common.action.edit',
    practiceApplyChange: '申请修改',
    practiceApplyResubmit: '重新发起',
    actionLog: '实践申请单流转情况',
  },

  action: {
    detail: '详情',
    edit: '@:common.action.edit',
    cancel: '@:common.action.cancel',
    search: '@:common.action.search',
    delete: '@:common.action.delete',
    unlock: '发起解锁',
    applyEdit: '申请修改',
    print: '打印',
    resubmit: '重新发起',
    createPracticeApply: '发起实践申请',
    draft: '暂存草稿',
    submit: '提交申请',
  },

  hint: {
    submitSucc: '提交成功',
    saveDraftSucc: '暂存成功',
    delTitle: '确认删除',
    delConfirm: '数据删除后将无法恢复，确认删除吗？',
    delSuccess: '删除成功',
    unlockTitle: '发起解锁',
    unlockConfirm: '是否发起解锁？',
    unlockSuccess: '解锁成功',
    unlockApplySuccess: '发起解锁成功',
    pleaseSelectTutor: '请选择导师',
  },

  columns: {
    nameAndNo: '学生名称/学号',
    practiceProgNo: '课程号',
    practiceWeeks: '周数',
    practiceUnit: '实践单位',
    practicePlace: '所在地点',
    cultivationProject: '培养项目名称',
    status: '状态',
    lockStatus: '锁定状态',
    creTime: '首次申请时间',
  },

  text: {
    statementSummary: '请确保以上信息真实有效，提交后将不能修改',
    statementAgree: [
      '我已清楚了解并同意',
      '《专业实践申请知情同意书》',
      '',
    ],
    pleaseAgree: '请同意《专业实践申请知情同意书》',
  },

  form,

  print,
};
