<script lang="ts" setup>
withDefaults(defineProps<{
  title: string;
  extra?: string;
  required?: boolean;
}>(), {
  extra: '',
  required: false,
});
</script>


<template>
  <div class="title-container">
    <div class="header">
      <div
        class="title"
        :class="{'required': required}"
      >
        {{ title }}
      </div>
      <slot name="header-right" />
    </div>

    <div
      v-if="extra"
      class="extra"
    >
      {{ extra }}
    </div>
  </div>
</template>


<style lang="less" scoped>
.title-container {
  margin-bottom: 12px;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .title {
    color: #3b3c3d;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;

    &.required {
      position: relative;

      &::before {
        position: absolute;
        top: 50%;
        left: -9px;
        display: inline-block;
        color: #ed4014;
        font-size: 14px;
        transform: translateY(-50%);
        content: "*";
      }
    }
  }

  .extra {
    margin-top: 2px;
    color: #e63c3c;
    font-weight: 300;
    font-size: 14px;
  }
}
</style>
