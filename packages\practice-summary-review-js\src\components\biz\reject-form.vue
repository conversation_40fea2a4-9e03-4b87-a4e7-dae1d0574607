<script lang="ts" setup>
import { ref } from 'vue';

import { formScrollIntoError } from '@/utils/form-scroll-into-error';
import { createInputRules } from '@/helps/rules';
import { namespaceT } from '@/helps/namespace-t';
import { InputType } from '@/consts/input-type';

import PimaInput from '@/components/common/pima-input.vue';

import type { ApproveFormType } from '@/types/approve-form-type';


const props = withDefaults(defineProps<{
  modelValue: ApproveFormType;
}>(), {});

interface EmitType {
  (e: 'on-save'): void
}
const emit = defineEmits<EmitType>();

const model = ref(props.modelValue);
const formRef = ref();
const t = namespaceT('practiceApprove');

const rules = {
  remark: [
    createInputRules(),
  ],
};

async function onSave() {
  const valid = await formRef.value.validate();
  if (!valid) {
    formScrollIntoError(formRef.value);
    return;
  }

  emit('on-save');
}

function resetFields() {
  formRef.value.resetFields();
}

defineExpose({
  resetFields,
  onSave,
});
</script>


<template>
  <Form
    ref="formRef"
    class="pima-form"
    :model="model"
    :rules="rules"
    :label-width="100"
  >
    <FormItem
      prop="remark"
      :label="t('label.rejectReason')"
    >
      <PimaInput
        v-model.trim="model.remark"
        :maxlength="300"
        :placeholder="t('placeholder.rejectReason')"
        :type="InputType.TEXTARER"
        show-word-limit
        :rows="5"
      />
    </FormItem>
  </Form>
</template>
