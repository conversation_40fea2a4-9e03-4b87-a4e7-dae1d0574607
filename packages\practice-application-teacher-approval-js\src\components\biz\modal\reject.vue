<script lang="ts" setup>
import { reactive, ref } from 'vue';

import PimaModal from '@/components/common/pima-modal.vue';

import { namespaceT } from '@/helps/namespace-t';
import { createInputRules } from '@/helps/rules';
import { InputType } from '@/consts/input-type';
import { executeWithDelay } from '@/utils/execution-with-delay';

interface ModelType {
  remark: string;
}


const emit = defineEmits<{
  'on-confirm': [remark:string],
  'on-cancel': [],
}>();

const formRef = ref();
const model = reactive<ModelType>({
  remark: undefined,
});


const tm = namespaceT('doubleSelection.modal.reject');
const onCancel = () => {
  emit('on-cancel');
};

const onConfirm = async () => {
  const valid = await formRef.value.validate();
  if (!valid) {
    return;
  }


  emit('on-confirm', model.remark);
};

const rules = {
  remark: [
    createInputRules(),
  ],
};

const onVisibleChange = (visible: boolean) => {
  if (!visible) {
    executeWithDelay(() => {
      Object.assign(model, {
        remark: undefined,
      });
      formRef.value.resetFields();
    });
  }
};

</script>


<template>
  <PimaModal
    :title="tm('title')"
    :width="480"
    :closable="false"
    v-bind="$attrs"
    @cancel="onCancel"
    @confirm="onConfirm"
    @on-visible-change="onVisibleChange"
  >
    <div class="content">
      <Form
        ref="formRef"
        class="pima-form full-width"
        :model="model"
        :rules="rules"
      >
        <FormItem
          prop="remark"
          :label="tm('label.remark')"
          class="no-colon"
        >
          <Input
            v-model.trim="model.remark"
            class="engineering-input-type-textarea pima-input-type-textarea"
            :rows="3"
            :type="InputType.TEXTAREA"
            :maxlength="300"
            show-word-limit
            :placeholder="tm('placeholder.remark')"
          />
        </FormItem>
      </form>
    </div>
  </PimaModal>
</template>


<style lang="less" scoped>
.content {
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(.ivu-form-item){
    .ivu-form-item-label {
      margin-right: 10px;
    }
  }
}
</style>
