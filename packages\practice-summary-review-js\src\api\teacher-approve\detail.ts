import { CommonApi } from '@/api/common/common-api';

import type { PracticeConclusionModelType } from '@/types/practice-conclusion';
import { namespaceT } from '@/helps/namespace-t';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
});


export class TeacherApprovalDetailApi extends CommonApi<{ model: PracticeConclusionModelType }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/teacher/practice-summary-approvers/${this.id}/sub`;
  }

  async send(): Promise<{ model: PracticeConclusionModelType }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.conclusionSubmit');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
