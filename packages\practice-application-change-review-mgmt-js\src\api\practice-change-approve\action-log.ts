import type { ActionLogType } from '^/types/action-log';
import { CommonApi, RequestParams } from '@/api/common/common-api';


export class ChangeApproveActionLogApi extends CommonApi<{ data: Array<ActionLogType> }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/change-request-rec/approvers/${this.id}/action-logs`;
  }

  defaultParams(): RequestParams {
    return {
      page: 1,
      limit: 99999,
    };
  }
}
