export default {
  searchBar: {
    status: 'Status',
  },

  placeholder: {
    keyword: 'Name,job number',
    user: 'Please enter your name and job number',
    select: 'Select',
    allStatus: 'All Status',
  },

  columns: {
    name: 'Name',
    userNo: 'Job number',
    role: 'Role',
    dept: 'Department',
    mainType: 'Responsible for recruitment category',
    recruitDept: 'Responsible for recruitment department',
    isEnable: 'Status',
    remark: 'Remarks',
    operatorAndTime: 'Last operator/ operation time',
    reviewManageSystem: 'Responsible for review business system',
  },

  action: {
    createUser: 'Create a new user',
    edit: 'Edit',
    del: 'Delete',
    save: 'Save',
  },

  hint: {
    confirmDelete: 'After deletion, the data will not be recoverable. Are you sure you want to continue?',
    deleteSuccess: 'Successfully deleted',
    addSucceeded: 'Successfully saved',
    editSucceeded: 'Modified successfully',
  },

  label: {
    user: 'User',
    userNo: 'Job number',
    dept: 'Department',
    email: 'Email',
    mobile: 'Contact number',
    role: 'Role',
    jobPostType: 'Responsible for recruitment category',
    recruitDept: 'Responsible for recruitment department',
    reviewManageSystem: '负责评审业务系统',
    status: 'Status',
    remark: 'Remarks',
  },

  title: {
    createUser: 'Create a new user',
    editUser: 'Edit User',
  },

  tip: {
    // eslint-disable-next-line max-len
    reviewManageSystem: 'Multiple selection is allowed, after setting, the user can see the relevant review type data of the business system',
  },
};
