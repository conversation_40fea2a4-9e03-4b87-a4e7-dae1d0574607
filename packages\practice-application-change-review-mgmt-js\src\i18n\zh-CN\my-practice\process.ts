export default {
  columns: {
    projectName: '@:myPractice.process.label.projectName',
    cultivationProject: '@:myPractice.applyMod.columns.cultivationProject',
    practiceProgNo: '@:myPractice.applyMod.columns.practiceProgNo',
    researchDirection: '@:myPractice.applyMod.columns.researchDirection',
    practiceUnit: '@:myPractice.applyMod.columns.practiceUnit',
    practicePlace: '@:myPractice.applyMod.columns.practicePlace',
    practiceStartDate: '@:myPractice.applyMod.columns.practiceStartDate',
    practiceEndDate: '@:myPractice.applyMod.columns.practiceEndDate',
    changeStatus: '@:myPractice.applyMod.columns.changeStatus',
    createInfo: '@:myPractice.applyMod.columns.createInfo',
  },

  action: {
    view: '@:myPractice.applyMod.action.view',
    applyEdit: '@:practiceApply.action.applyEdit',
    print: '@:practiceApply.action.print',
    resubmit: '@:practiceApply.action.resubmit',
    draft: '@:practiceApply.action.draft',
    submit: '@:practiceApply.action.submit',
    edit: '@:common.action.edit',
    cancel: '@:common.action.cancel',
    delete: '@:common.action.delete',
    addProcess: '点击添加实践过程记录',
    approve: '审核',
    pass: '审核通过',
    reject: '审核驳回',
    modify: '@:common.action.modify',
    save: '@:common.action.save',
    review: '评定',
  },

  hint: {
    submitSucc: '@:practiceApply.hint.submitSucc',
    saveDraftSucc: '@:practiceApply.hint.saveDraftSucc',
    delTitle: '确认删除',
    delConfirm: '数据删除后将无法恢复，确认删除吗？',
    delSuccess: '删除成功',
    approveSuccess: '审核成功',
    saveSucc: '保存成功',
    draftDataExists: '当前实践过程记录已存在“草稿”数据，无法进行添加',
  },

  label: {
    submitPerson: '提交人',
    submitTime: '提交时间',
    practiceProgNo: '课程号',
    userNo: '学号',
    userName: '姓名',
    projectName: '实践课题名称',
    startDate: '开始时间',
    endDate: '结束时间',
    content: '内容说明',
    attachment: '附件',
    modifyContent: '@:practiceApply.form.label.modifyContent',
  },

  placeholder: {
    input: '@:common.placeholder.input',
    select: '@:common.placeholder.select',
    modifyContent: '@:practiceApply.form.placeholder.modifyContent',
  },

  title: {
    add: '添加实践过程记录',
    practiceProcess: '实践过程记录',
    edit: '编辑',
    modify: '@:common.action.modify',
    view: '查看',
    actionLogTitle: '实践过程记录流转情况',
    changeActionLogTitle: '实践过程记录异动流转情况',
    restart: '重新发起',
    changeApply: '申请修改',
    pass: '确认通过',
  },

  print: {
    title: '清华大学深圳国际研究生院-研究生专业实践记录表',
    processPeriod: '实践记录时段：',
    content: '研究生本人如实记录实践内容：',
    sign: '（签字）：',
    attachPage: '（本表可附页）',
    offCampusRemark: '校外导师意见：',
    onCampusRemark: '校内导师意见：',
    tip: '注：此表每四周填写一次，经导师签字后以学科为单位交培养处实验实践教学中心备案。',
  },
};
