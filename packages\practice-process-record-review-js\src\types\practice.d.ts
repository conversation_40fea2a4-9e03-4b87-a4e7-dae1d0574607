export interface SupervisorsDetailType {
  id: number;
  supervisorType: string;
  supervisorUserId: number;
  supervisorUserNo: string;
  supervisorName: string;
  jobTitle: string;
  researchDomain: string;
  workUnit: string;
  phone: string;
  email: string;
}

interface UserType {
  userId: number;
  name: string;
  userNo: string;
  id: number;
}

export interface PracticeApplyBaseInfoType {
  user?: UserType;
  userNo: string;
  userName: string;
  phone: string;
  email: string;
  cultivationProject: string;
  freshGraduateInd: string;
  practiceProgNo: string;
  supervisor: string;
  researchDirection: string;
  applyTime?: string;
}

export interface PracticeApplyTuitionMemberType {
  onCampusSupervisorUserId: number;
  offCampusSupervisorUserId: string;
  onCampusSupervisor?: SupervisorsType,
  offCampusSupervisor?: SupervisorsType,
}

export interface PracticeDate {
  practiceStartDate: string | Date;
  practiceEndDate: string | Date;
}

export interface PracticeApplyContentType {
  practiceUnit: string;
  practicePlace: string;
  practiceUnitInCharge: string;
  practiceUnitInChargePhone: string;
  practiceUnitType: string;
  progFinishInfo: string;
  practiceDate?: PracticeDate;
  practiceWeeks: number;
  practiceContent: string;
  practiceProgress: string;
  modifyContent: string;
  practiceStartDate?: string | Date;
  practiceEndDate?: string | Date;
}

export interface PracticeDetailType {
  baseInfo: PracticeApplyBaseInfoType;
  tuitionMember: PracticeApplyTuitionMemberType;
  content: PracticeApplyContentType;
}


export interface PracticeDetailApiType extends PracticeApplyBaseInfoType
  , PracticeApplyTuitionMemberType, PracticeApplyContentType {
  campusSupervisorList: SupervisorsDetailType[];
}

// 实践概览字段
export interface PracticeSummaryType {
  userName: string;
  userNo: string;
  practiceUnit: string;
  practicePlace: string;
  onCampusSupervisor: string;
  offCampusSupervisor: string;
  practiceStartDate: string;
  practiceEndDate: string;
  progressNode?: string;
}
