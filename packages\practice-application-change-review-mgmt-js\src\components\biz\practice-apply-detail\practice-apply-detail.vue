<script setup lang="ts">
import { type Ref, ref, computed } from 'vue';

import ContentBlock from '@/components/common/content-block.vue';
import NavFormWrap from '@/components/common/nav-form-wrap.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import ActionLog from '@/components/biz/action-log.vue';

import type { PracticeDetailType } from '@/types/practice';
import type { ActionLogType } from '@/types/action-log';
import { useNavigationBar } from '@/uses/navigation-bar';
import { namespaceT } from '@/helps/namespace-t';
import { getPageComponents } from './hooks/get-page-components';


interface ComponentRef {
  [key: string]: object;
  validate: () => Promise<boolean>;
}

interface Props {
  modelValue: PracticeDetailType,
  changeApply?: boolean, // 是否申请修改
  actionLog?: ActionLogType[],
  actionLogTitle?: string;
  isManage?: boolean; // 是否管理端
  isTeacher?: boolean; // 是否教师端
}

const props = withDefaults(defineProps<Props>(), {
  changeApply: false,
  actionLog: () => [],
  actionLogTitle: '',
  isManage: false,
  isTeacher: false,
});

interface EmitType {
  (e: 'update:modelValue', val: object): void
}
const emit = defineEmits<EmitType>();

const navBar = useNavigationBar();
const wrapperFormRef = ref();
const componentRefs: Ref<ComponentRef[]> = ref([]);
const t = namespaceT('practiceApply.form');
const tt = namespaceT('practiceApply.title');

const model = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
});

async function validate() {
  const validationResults = await Promise.all(
    componentRefs.value.map(async (item) => {
      if (!item.validate) return true;

      const res = await item.validate();

      return res;
    }),
  );

  const isValid = validationResults.every((result) => result);
  return isValid;
}

function scrollToIndex(index: number) {
  const item = navBar.list[index];

  item.scrollIntoView();
}

defineExpose({
  validate,
  scrollToIndex,
});
</script>


<template>
  <NavFormWrap
    ref="wrapperFormRef"
    :nav-bar="navBar"
  >
    <WrapperFormTitle :title="t('detail')">
      <ContentBlock
        v-for="(block, index) in getPageComponents()"
        :key="index"
        :title="block.title"
        :title-extra="block.hint"
        :order="block.order"
        :nav-bar="navBar"
      >
        <component
          :is="block.component"
          :ref="el => componentRefs[index] = el"
          v-model:[block.modelName]="model[block.modelName]"
          :change-apply="changeApply"
          :is-manage="isManage"
          :is-teacher="isTeacher"
        />
      </ContentBlock>
    </WrapperFormTitle>

    <template #right>
      <ActionLog
        :data-source="actionLog"
        :title="actionLogTitle || tt('actionLog')"
      />
    </template>
  </NavFormWrap>
</template>
