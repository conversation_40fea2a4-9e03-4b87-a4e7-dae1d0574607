export default {
  searchBar: {
    status: 'Status',
  },

  title: {
    createRole: 'Create a new role',
    editRole: 'Edit Role',
    authSet: '{name}>>Permission Settings',
  },

  columns: {
    code: 'Code',
    name: 'Role Name',
    description: 'Role Description',
    isEnable: 'Status',
    operatorAndTime: 'Last operator/ operation time',
  },

  action: {
    createRole: 'Create a new role',
    edit: 'Edit',
    authSet: 'Permission Settings',
    del: 'Delete',
    save: 'Save',
  },

  label: {
    code: 'Role code',
    name: 'Role Name',
    description: 'Description',
    isEnable: 'Status',
  },

  placeholder: {
    keyword: 'Role code,role name,description,operator',
    code: 'Please enter the role code',
    name: 'Please enter a role name',
    allStatus: 'All Status',
  },

  hint: {
    confirmDelete: 'After deletion, the data will not be recoverable. Are you sure you want to continue?',
    addSucceeded: 'Successfully saved',
    editSucceeded: 'Modified successfully',
    deleteSuccess: 'Successfully deleted',
  },

  tip: {
    isEnable: 'After disabling, the role permission will not take effect',
  },

  authSet: {
    serviceType: {
      pc: 'PC end',
      weChat: 'WeChat end',
      miniProgram: 'Mini program end',
    },
    step: {
      authOperate: 'Permission operation',
      dataRange: 'Data visible range',
    },
    place: {
      chooseDept: 'Select department',
    },
  },
};
