export default {
  title: 'Apply for practice',
  detail: 'Practice application details',

  selectSchoolTutor: {
    selectTutor: 'Select supervisor',
    selectSchoolTutor: 'Select on-campus supervisor',
    selectExternalTutor: 'Select off-campus supervisor',
    reselect: 'Re-select',
  },

  navTitle: {
    baseInfo: 'Student basic information',
    tuitionMember: 'Supervisory group members',
    practiceContent: 'Content and plan',
  },

  subTitle: {
    schoolTutor: 'On-campus supervisor',
    externalTutor: 'Off-campus supervisor',
    practiceUnit: 'The institution where you practiced',
    progFinishInfo: 'Course completion status in training plan',
    practiceOpenTime: 'Planned start and end time for professional practice',
    practiceContent: 'Practice plan and content',
    practiceProgress: 'Practice progress',
  },

  label: {
    userNo: 'Student ID',
    userName: 'Name',
    mobile: 'Contact number',
    email: 'Email address',
    cultivationProject: 'Training program name',
    freshGraduateInd: 'Were you a fresh graduate before enrollment?',
    freshGraduateIndNotice: '(Select based on actual circumstances)',
    practiceProgNo: 'Professional practice course code',
    tutorName: 'Supervisor',
    researchDirection: 'Research direction',
    tutor: {
      name: 'Name',
      jobTitle: 'Title',
      researchDomain: 'Major and research areas',
      workUnit: 'Affiliated organization',
      mobile: 'Contact number',
      email: 'Email address',
    },
    practiceUnit: {
      name: 'Name',
      practiceUnitType: 'Type of enterprise',
      practiceUnitInCharge: 'Name of person in charge',
      practiceUnitInChargePhone: 'Contact number of person in charge',
      practicePlace: 'Location',
    },
    modifyContent: 'Description of modification',
  },

  placeholder: {
    user: 'Please enter name or student ID to search',
    modifyContent: "Please specify the scope of the modifications in this application for the supervisor's review",
    chooseActualSituation: 'Select based on actual circumstances',
  },

  text: {
    // eslint-disable-next-line max-len
    practiceUnit: 'Please ensure that the institution where you will be practicing is able to issue an internship certificate. Enter the registered name exactly as it appears on Qichacha, consistent with the information on the business license.',
    // eslint-disable-next-line max-len
    tuitionMember: '1. Your application will be sent to the supervisor via email. Please fill it in carefully! 2. If the off-campus supervisor is not yet confirmed, you may leave it blank and complete it later',
    totalWeeks: [
      'Total',
      'Weeks',
    ],
  },

  hint: {
    maxLength: 'Maximum {max} characters',
  },
};
