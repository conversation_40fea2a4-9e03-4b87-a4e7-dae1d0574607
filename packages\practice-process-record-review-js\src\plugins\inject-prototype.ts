import { App } from 'vue';
import eventBus from '@/utils/event-bus';
import { valueByLocale } from '@/helps/locale';
import { dateFormatSTZ, dateFormat } from '@/helps/date';


export const InjectPrototypePlugin = {
  install(app: App) {
    Object.assign(app.config.globalProperties, {
      $eventBus: eventBus,
      $dateFormatSTZ: dateFormatSTZ,
      $dateFormat: dateFormat,
      $judgeLanguage: valueByLocale,
    });
  },
};
