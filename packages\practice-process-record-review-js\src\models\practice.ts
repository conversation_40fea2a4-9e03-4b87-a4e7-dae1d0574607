import type {
  PracticeApplyBaseInfoType,
  PracticeApplyTuitionMemberType,
  PracticeApplyContentType,
  PracticeDetailType,
  PracticeSummaryType,
} from '@/types/practice';
import { createSupervisorsModel } from './supervisors';

export function createPracticeApplyBaseInfoModel(): PracticeApplyBaseInfoType {
  return {
    user: null,
    userNo: null,
    userName: null,
    phone: null,
    email: null,
    cultivationProject: null,
    freshGraduateInd: null,
    practiceProgNo: null,
    supervisor: null,
    researchDirection: null,
    applyTime: null,
  };
}

export function createPracticeApplyTuitionMemberModel(): PracticeApplyTuitionMemberType {
  return {
    onCampusSupervisorUserId: null,
    offCampusSupervisorUserId: null,
    offCampusSupervisor: createSupervisorsModel(),
    onCampusSupervisor: createSupervisorsModel(),
  };
}

export function createPracticeApplyContentModel(): PracticeApplyContentType {
  return {
    practiceUnit: null,
    practicePlace: null,
    practiceUnitInCharge: null,
    practiceUnitInChargePhone: null,
    practiceUnitType: null,
    progFinishInfo: null,
    practiceDate: {
      practiceStartDate: null,
      practiceEndDate: null,
    },
    practiceWeeks: null,
    practiceContent: null,
    practiceProgress: null,
    modifyContent: null,
    practiceStartDate: null,
    practiceEndDate: null,
  };
}

export function createPracticeDetailModel(): PracticeDetailType {
  return {
    baseInfo: createPracticeApplyBaseInfoModel(),
    tuitionMember: createPracticeApplyTuitionMemberModel(),
    content: createPracticeApplyContentModel(),
  };
}

// 实践概览字段
export function createPracticeSummaryModel(): PracticeSummaryType {
  return {
    userName: null,
    userNo: null,
    practiceUnit: null,
    practicePlace: null,
    onCampusSupervisor: null,
    offCampusSupervisor: null,
    practiceStartDate: null,
    practiceEndDate: null,
    progressNode: null,
  };
}
