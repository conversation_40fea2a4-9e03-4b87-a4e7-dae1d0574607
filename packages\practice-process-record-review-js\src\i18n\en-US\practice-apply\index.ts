import form from './form';
import print from './print';

export default {
  searchBar: {
    status: 'Status',
    statusPlace: 'All statuses',
    lockStatus: 'Lock status',
    lockStatusPlace: 'All lock statuses',
    // eslint-disable-next-line max-len
    keyword: 'Please enter the name of the institution where you will be practicing. Use "\\" to separate multiple search terms',
    practiceProgNo: 'Course code',
    practiceUnit: 'The institution where you practiced',
    practicePlace: 'Location',
    practicePlacePlace: 'Use "\\" to separate multiple search terms',
    cultivationProject: 'Training program name',
    applyTime: 'Application time',
    tutorName: 'Please enter name',
    tutorMobile: 'Please enter contact number',
  },

  title: {
    practiceApply: 'Initiate practice application',
    applyStatement: 'Professional practice informed consent form',
    practiceApplyDetail: 'Practice application details',
    practiceApplyEdit: '@:common.action.edit',
    practiceApplyChange: 'Request modification',
    practiceApplyResubmit: 'Re-initiate',
    actionLog: 'Practice application workflow status',
  },

  action: {
    detail: 'Details',
    edit: '@:common.action.edit',
    cancel: '@:common.action.cancel',
    search: '@:common.action.search',
    delete: '@:common.action.delete',
    unlock: 'Initiate unlock',
    applyEdit: 'Request modification',
    print: 'Print',
    resubmit: 'Re-initiate',
    createPracticeApply: 'Initiate practice application',
    draft: 'Save as draft',
    submit: 'Submit application',
  },

  hint: {
    submitSucc: 'Submitted successfully',
    saveDraftSucc: 'Saved as draft successfully',
    delTitle: 'Confirm to delete',
    delConfirm: 'The data cannot be retrieved once deleted. Do you still want to delete it?',
    delSuccess: 'Deleted successfully',
    unlockTitle: 'Initiate unlock',
    unlockConfirm: 'Do you want to initiate unlock?',
    unlockSuccess: 'Unlock successfully',
    unlockApplySuccess: 'Unlock initiated successfully',
    pleaseSelectTutor: 'Please select supervisor',
  },

  columns: {
    nameAndNo: 'Student name / student ID',
    practiceProgNo: 'Course code',
    practiceWeeks: 'Weeks',
    practiceUnit: 'The institution where you practiced',
    practicePlace: 'Location',
    cultivationProject: 'Training program name',
    status: 'Status',
    lockStatus: 'Lock status',
    creTime: 'First application time',
  },

  text: {
    statementSummary: 'Please ensure the information is accurate and valid. It cannot be modified after submission',
    statementAgree: [
      'I have read and agreed to the',
      'Professional Practice Informed Consent Form',
      '',
    ],
    pleaseAgree: 'Please agree to the Professional Practice Informed Consent Form',
  },

  form,

  print,
};
