import _ from 'lodash';


export interface TreeNode {
  // 假设每个节点有一个标识其自身的属性（这里以 'name' 为例），以及可能的子节点数组
  name: string;
  children?: TreeNode[]; // 子节点数组，可选，因为根节点可能没有子节点
  [key: string]: unknown; // 允许其他属性存在，因为节点可能包含额外信息
}

/**
 * 生成器函数，用于遍历森林（包含多个树的集合）中的所有节点。
 * 采用深度优先搜索策略递归遍历每棵树的节点。
 *
 * @param  [forest=[]] - 代表森林的数组，每个元素是一个表示树节点的对象。
 * @yields 依次产生森林中每个节点。
 *
 * 示例用法：
 * const trees = [
 *   { value: 1, children: [{ value: 2 }, { value: 3 }] },
 *   { value: 4, children: [{ value: 5 }, { value: 6, children: [{ value: 7 }] }] }
 * ];
 * for (const node of forestNodes(trees)) {
 *   console.log(node.value);
 * }
 *
 * 此函数首先遍历顶级节点，然后递归遍历每个子树的所有子节点，
 * 直至森林中的所有节点都被访问过。
 */
export function* forestNodes(forest = []) {
  for (let i = 0; i < forest.length; i += 1) {
    const treeNode = forest[i];
    yield treeNode; // 产出当前节点
    yield* forestNodes(treeNode.children); // 递归遍历当前节点的子树，并展平产出其所有子节点
  }
}

/**
 * 重命名树节点。
 *
 * 此函数用于递归地遍历树结构，并将指定名称的节点重命名为新名称。如果节点有子节点，子节点的名称也会被重命名。
 * 主要用于数据结构的变换，例如在图形用户界面的组件树中更改组件名称。
 *
 * @param tree 树结构，可以是单个节点或节点数组。
 * @param oldNodeName 需要被重命名的节点的原始名称。
 * @param newNodeName 节点的新名称。
 * @returns 返回一个新的树结构，其中原始名称的节点已被重命名为新名称。
 */
export function renameNodes(tree: TreeNode[], oldNodeName: string, newNodeName: string): TreeNode[] {
  // 如果树结构是一个数组，递归处理数组中的每个节点。
  if (Array.isArray(tree)) {
    return tree.map((oldNode) => {
      // 检查当前节点是否包含需要被重命名的属性。
      if (oldNodeName in oldNode) {
        // 克隆当前节点，以避免修改原始数据。
        const newNode = _.cloneDeep(oldNode);
        const currChildren = newNode[oldNodeName] as TreeNode[];
        // 递归重命名当前节点的子节点。
        const children = renameNodes(currChildren, oldNodeName, newNodeName);
        // 如果子节点数量大于0，将子节点列表赋值给新名称的属性。
        if (children.length > 0) {
          newNode[newNodeName] = children;
        }
        // 删除原始名称的属性。
        delete newNode[oldNodeName];
        // 返回修改后的节点。
        return newNode;
      }
      // 如果当前节点不需要重命名，直接返回原节点。
      return oldNode;
    });
  }
  // 如果树结构不是数组，返回空数组。
  return [];
}

export function injectNodesProps(tree, props) {
  // eslint-disable-next-line no-restricted-syntax
  for (const item of forestNodes(tree)) {
    Object.assign(item, {
      ...props,
    });
  }

  return tree;
}
