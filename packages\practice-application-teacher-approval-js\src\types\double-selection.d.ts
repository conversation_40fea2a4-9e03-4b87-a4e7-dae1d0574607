import type { AttachmentVO } from '@/types/attachment';

export interface JobPostDetailType {
  /**
     * 申请时间
     */
  applyTime?: Date;
  /**
     * 审批附件列表
     */
  attachmentList?: AttachmentVO[];
  /**
     * 基地联系人
     */
  baseContact?: string;
  /**
     * 基地详细地址
     */
  baseDetailAddr?: string;
  /**
     * 基地名称
     */
  baseName?: string;
  /**
     * 简介
     */
  baseSummary?: string;
  /**
     * 能否审批
     */
  canApproval?: boolean;
  /**
     * 称呼 数据字典[服务:基地管理,服务编码:tsinghua-base-management,字典类型:TITLE]
     */
  contactTitle?: string;
  /**
     * 邮箱地址     aes加密
     */
  email?: string;
  /**
     * 截止时间
     */
  endTime?: Date;
  /**
     * ID
     */
  id?: number;
  /**
     * 工作内容
     */
  jobContent?: string;
  /**
     * 岗位名称
     */
  jobTitle?: string;
  /**
     * 岗位类型 数据字典[字典类型:JOB_POST_JOB_TYPE]
     */
  jobType?: string;
  /**
     * 所需专业
     */
  majorReq?: string;
  /**
     * 手机号码     aes加密
     */
  mobile?: string;
  /**
     * 其他要求
     */
  othReq?: string;
  /**
     * 实践地点
     */
  practicePlace?: string;
  /**
     * 实践地点_区名称（中英文）
     */
  practicePlaceAddrAreaName?: string;
  /**
     * 实践地点_市名称（中英文）
     */
  practicePlaceAddrCityName?: string;
  /**
     * 实践地点_省名称（中英文）
     */
  practicePlaceAddrProvName?: string;
  /**
     * 需求人數
     */
  recruitAmt?: number;
  /**
     * 审批备注
     */
  remark?: string;
  /**
     * 研究方向
     */
  researchField?: string;
  /**
     * 性别要求 数据字典[字典类型:JOB_POST_SEX]
     */
  sex?: string;
  /**
     * 审核状态 数据字典[字典类型:APPROVER_STATUS]
     */
  status?: string;
}
