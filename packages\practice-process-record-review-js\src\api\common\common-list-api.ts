import _ from 'lodash';
import { PaginationParamsOption, paginationParams } from '^/helps/api';
import { CommonApi } from './common-api';


export class CommonListApi<T> extends CommonApi<CommonApiListData<T>> {
  set params(value: PaginationParamsOption) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    super.params = paginationParams(value);
  }

  async send() : Promise<CommonApiListData<T>> {
    const res = await super.send();
    const data: T[] = _.get(res, 'data', []);
    const total: number = _.get(res, 'total', 0);
    return {
      data,
      total,
    };
  }
}
