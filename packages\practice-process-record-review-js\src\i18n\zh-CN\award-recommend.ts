export default {
  searchBar: {
    applyStartTime: '开始发起时间',
    applyEndTime: '结束发起时间',
    keyword: '请输入岗位名称、单位名称',
  },

  title: {
    add: '新增实践奖推荐名单',
    edit: '编辑实践奖推荐名单',
    baseInfo: '基本信息',
    awardNameList: '实践奖推荐名单',
    recommendNameList: '推优名单列表',
    view: '查看',
    resubmit: '重新发起',
    projectInChargeSign: '项目负责人签字',
    actionLogTitle: '申请日志',
    sign: '签字确认',
  },

  label: {
    awardName: '实践奖名称',
    cultivationProject: '培养项目名称',
    onCampusSupervisorName: '校内导师',
    offCampusSupervisorName: '校外导师',
  },

  hint: {
    delTitle: '确认删除',
    delConfirm: '数据删除后将无法恢复，确认删除吗？',
    delSuccess: '删除成功',
    submitSucc: '发起成功',
    saveDraftSucc: '暂存草稿成功',
    saveSucc: '保存成功',
  },

  error: {
    pleaseSelectUser: '请添加推荐学员',
  },

  action: {
    add: '新增时间奖推荐名单',
    downLoad: '下载推荐名单',
    reSubmit: '重新发起',
    sign: '签字确认',
    draft: '暂存草稿',
    submit: '发起',
    addUser: '添加推荐学员',
    reject: '驳回',
  },

  columns: {
    awardName: '@:awardRecommend.label.awardName',
    cultivationProject: '@:awardRecommend.label.cultivationProject',
    creator: '创建人',
    submitTime: '发起时间',
    status: '状态',
  },
};
