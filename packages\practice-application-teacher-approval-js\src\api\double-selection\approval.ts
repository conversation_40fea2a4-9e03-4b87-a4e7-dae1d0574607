import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';


enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
  JOB_STATUS_ERROR = 'JOB_STATUS_ERROR',
  APPROVAL_STATUS_ERROR = 'APPROVAL_STATUS_ERROR',
}


export class A<PERSON>roval<PERSON><PERSON> extends CommonApi<void> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/job-post-approvers/${this.id}`;
  }

  method(): RequestMethod {
    return 'POST';
  }

  async send(): Promise<void> {
    try {
      await super.send();
    } catch (error) {
      const t = namespaceT('apiErrors.doubleSelection.approval');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
        case ErrorCodes.APPROVAL_STATUS_ERROR:
        case ErrorCodes.JOB_STATUS_ERROR:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
