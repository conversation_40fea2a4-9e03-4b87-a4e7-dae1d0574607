import { namespaceT } from '@/helps/namespace-t';


const tm = namespaceT('common.error');

export function createInputRules({
  type = 'string',
  message = tm('thisFieldIsRequired'),
} = {
  type: 'string',
  message: tm('thisFieldIsRequired'),
}) {
  return {
    type,
    message,
    required: true,
    trigger: 'blur',
  };
}

export function createSelectRules({
  type = 'string',
  message = tm('thisFieldMustBeSelected'),
} = {
  type: 'string',
  message: tm('thisFieldMustBeSelected'),
}) {
  return {
    type,
    message,
    required: true,
    trigger: 'change',
  };
}

export function createCheckboxRules() {
  return {
    type: 'array',
    required: true,
    message: tm('thisFieldMustBeSelected'),
    trigger: 'change',
    min: 1,
  };
}

export function createMobileRule() {
  return {
    pattern: /^1[3-9][0-9]{9}$/,
    message: tm('mobileNumberFormatIsIncorrect'),
    trigger: 'blur',
  };
}

export function createEmailRule() {
  return {
    type: 'email',
    message: tm('emailFormatIsIncorrect'),
    trigger: 'blur',
  };
}

export function createEditorRule() {
  return {
    type: 'string',
    message: tm('thisFieldIsRequired'),
    required: true,
    trigger: 'change',
  };
}
