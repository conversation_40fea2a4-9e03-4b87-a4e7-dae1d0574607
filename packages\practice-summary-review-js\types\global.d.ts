export {};

declare global {
  declare module 'pimaRemoteUI/*';

  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    __INITIAL_STATE__: Record<string, any>;
  }

  type GetMenuName = {
    (fn: (SM: SiderMenuCodes) => string): string;
  };

  interface Can {
    (fn: (P: typeof Auth) => string): boolean;
  }

  type FormRefType = InstanceType<typeof Form> & {
    validate: (cb?: (valid?: boolean) => boolean | void) => Promise<boolean>;
    resetFields: () => void;
    validateField: ValidateField;
  };

  // 添加 QueryTableResult 类型
  type QueryTableResult<S = unknown, A = unknown, T = unknown> =
  import('@/types/query-table').QueryTableResult<S, A, T>;

}
