export default {
  action: {
    view: '@:myPractice.applyMod.action.view',
    add: 'Click to upload review materials',
    cancel: '@:common.action.cancel',
    draft: '@:myPractice.process.action.draft',
    submit: '@:myPractice.process.action.submit',
    save: '@:myPractice.process.action.save',
    modify: '@:myPractice.process.action.modify',
    review: 'Evaluate',
    previewOnline: 'Online preview',
    reject: 'Rejected',
    pass: 'Confirm evaluation',
  },

  title: {
    add: 'Add review materials',
    reviewMaterials: 'Review materials',
    edit: '@:myPractice.process.title.edit',
    view: '@:myPractice.process.title.view',
    restart: '@:myPractice.process.title.restart',
    changeApply: '@:myPractice.process.title.changeApply',
    actionLogTitle: 'Review materials workflow status',
    review: 'Grade evaluation',
  },

  hint: {
    saveDraftSucc: '@:myPractice.process.hint.saveDraftSucc',
    submitSucc: '@:myPractice.process.hint.submitSucc',
    delSuccess: '@:myPractice.process.hint.delSuccess',
    delTitle: '@:myPractice.process.hint.delTitle',
    delConfirm: '@:myPractice.process.hint.delConfirm',
    saveSucc: '@:myPractice.process.hint.saveSucc',
    passTitle: 'Confirm evaluation',
    passConfirm: 'Confirm the evaluation?',
    approveSuccess: '@:myPractice.process.hint.approveSuccess',
  },

  label: {
    userNo: '@:myPractice.process.label.userNo',
    userName: '@:myPractice.process.label.userName',
    projectName: '@:myPractice.process.label.projectName',
    practiceUnit: '@:myPractice.conclusion.label.practiceUnit',
    cultivationProject: '@:practiceApply.form.label.cultivationProject',
    supervisor: '@:myPractice.conclusion.label.supervisor',
    practiceTime: 'Practice duration',
    practiceProgNo: '@:practiceApply.form.label.practiceProgNo',
    summaryReportAttachmentList: 'Summary report',
    practiceProvenAttachmentList: 'Proof of practice',
  },

  text: {
    attachmentTip: 'Please upload a PDF file no larger than 20MB',
  },

  evalResult: {
    y: 'Pass',
    n: 'Fail',
  },
};
