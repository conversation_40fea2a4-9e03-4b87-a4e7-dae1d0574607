import { CommonListApi } from '@/api/common/common-list-api';
import type { AwardManageListType } from '^/types/award-manage';


export class AwardManageListApi extends CommonListApi<AwardManageListType> {
  url() {
    // TODO:假接口
    return '/award-manage';
  }

  send():Promise<{ total: number, data: AwardManageListType[] }> {
    const res = {
      total: 30,
      data: [
        {
          id: 1,
          awardName: '2023-2024学年清华大学深圳国际研究生院专业实践奖',
          cultivationProject: 'ENGINEERING_MASTER_DOCTOR_REFORM_MECHANICAL',
          userName: '张三',
          userNo: '017017',
          onCampusSupervisorName: '马辉',
          offCampusSupervisorName: '何波',
          practiceProject: '人工智能项目',
          awardResult: '',
          status: '待校外导师签字',
          canDeal: false,
        },
        {
          id: 2,
          awardName: '2023-2024学年清华大学深圳国际研究生院专业实践奖',
          cultivationProject: 'ENGINEERING_MASTER_DOCTOR_REFORM_MECHANICAL',
          userName: '张三',
          userNo: '017017',
          onCampusSupervisorName: '马辉',
          offCampusSupervisorName: '何波',
          practiceProject: '人工智能项目',
          awardResult: '',
          status: '待校内导师签字',
          canDeal: false,
        },
        {
          id: 3,
          awardName: '2023-2024学年清华大学深圳国际研究生院专业实践奖',
          cultivationProject: 'ENGINEERING_MASTER_DOCTOR_REFORM_MECHANICAL',
          userName: '张三',
          userNo: '017017',
          onCampusSupervisorName: '马辉',
          offCampusSupervisorName: '何波',
          practiceProject: '人工智能项目',
          awardResult: '',
          status: '待录入结果',
          canDeal: true,
        },
        {
          id: 4,
          awardName: '2023-2024学年清华大学深圳国际研究生院专业实践奖',
          cultivationProject: 'ENGINEERING_MASTER_DOCTOR_REFORM_MECHANICAL',
          userName: '张三',
          userNo: '017017',
          onCampusSupervisorName: '马辉',
          offCampusSupervisorName: '何波',
          practiceProject: '人工智能项目',
          awardResult: '专业实践奖三等奖',
          status: '已录入',
          canDeal: false,
        },
      ],
    };

    return new Promise((resolve) => {
      resolve(res);
    });
  }
}
