<script lang="ts" setup>
import ButtonGroup from '@/components/biz/button-group.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import TitleBar from '@/components/common/title-bar.vue';
import ActionLog from '@/components/biz/action-log.vue';
import PracticeConclusionDetail from '@/components/biz/practice-conclusion-detail.vue';
import NavFormWrap from '@/components/common/nav-form-wrap.vue';

import { namespaceT } from '@/helps/namespace-t';
import type { PracticeConclusionModelType } from '@/types/practice-conclusion';
import { ActionLogType } from '@/types/action-log';

type Props = {
  loading: boolean;
  modelValue: PracticeConclusionModelType;
  actionLog: ActionLogType[];
};
withDefaults(defineProps<Props>(), {
  loading: false,
});
const emit = defineEmits(['on-cancel', 'on-reject', 'on-pass']);

const t = namespaceT('myPractice.conclusion');

const handleGoBack = () => {
  emit('on-cancel');
};
</script>


<template>
  <div class="engineering-theme">
    <div class="pima-form-page to-do">
      <TitleBar
        go-back
        :title="t('action.view')"
        @go-back="handleGoBack"
      />

      <WrapperForm
        :loading="loading"
      >
        <NavFormWrap :nav-bar="null">
          <PracticeConclusionDetail
            :model-value="modelValue"
            is-teacher
          />


          <template #right>
            <ActionLog
              :data-source="actionLog"
              :title="t('title.actionLogTitle')"
            />
          </template>
        </NavFormWrap>

        <template
          v-if="modelValue.canApproval"
          #action
        >
          <ButtonGroup
            @on-cancel="handleGoBack"
            @on-reject="emit('on-reject')"
            @on-pass="emit('on-pass')"
          />
        </template>
      </WrapperForm>
    </div>
  </div>
</template>
