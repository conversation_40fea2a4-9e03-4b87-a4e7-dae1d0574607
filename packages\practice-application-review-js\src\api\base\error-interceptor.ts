import emitter from '@/utils/event-bus';
import { namespaceT } from '@/helps/namespace-t';


const ErrorCode = Object.freeze({
  F_GET_TOKEN_FAILED: 'F_GET_TOKEN_FAILED', // Node服务器端错误码，表示获取Token失败
  F_REFRESH_TOKEN_FAILED: 'F_REFRESH_TOKEN_FAILED', // Node服务器端错误码，表示刷新Token失败
  ACCESS_CHECK: 'ACCESS_CHECK',
  AUTHORIZATION_NULL_AND_VOID: 'AUTHORIZATION_NULL_AND_VOID',
  INVALID: 'INVALID',
  ACCOUNT_DISABLE: 'ACCOUNT_DISABLE',
  ACCOUNT_FREEZE_OR_DEACTIVATE: 'ACCOUNT_FREEZE_OR_DEACTIVATE',
  ACCOUNT_EXCEPTION: 'ACCOUNT_EXCEPTION',
  ECONNABORTED: 'ECONNABORTED',
  OTHER: 'OTHER',
});

const C = ErrorCode;

export function errorInterceptor(error) {
  // 如果响应为401状态码错误，则通知相关事件
  if (error.response?.status === 401) {
    emitter.emit('userUnauthorizedError');
    throw error;
  }

  // 用户AccessToken失效
  if ([C.F_GET_TOKEN_FAILED, C.ACCESS_CHECK, C.AUTHORIZATION_NULL_AND_VOID].includes(error.code)) {
    emitter.emit('invalidAccessTokenError');
    throw error;
  }

  // 用户RefreshToken失效
  if (error.code === C.F_REFRESH_TOKEN_FAILED
    || (error.code === C.INVALID && error.response?.data?.errorList?.INVALID === 'refreshToken')) {
    emitter.emit('invalidRefreshTokenError');
    throw error;
  }

  if ([C.ACCOUNT_DISABLE, C.ACCOUNT_FREEZE_OR_DEACTIVATE, C.ACCOUNT_EXCEPTION].includes(error.code)) {
    emitter.emit('catchAccountError', error.code);
    throw error;
  }

  // 超时
  if (error.response?.status === 408
    || (error.code === C.ECONNABORTED && /timeout of \d+ms exceeded$/.test(error.message))) {
    const t = namespaceT('common.error');
    const err: BaseError = new Error(t('timeoutError'));
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // 未知错误
  if (error.code === C.OTHER) {
    const t = namespaceT('common.error');
    const err: BaseError = new Error(t('unknownError'));
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // Network Error 由浏览器抛出 （chrome | safari | edge | firefox 测试可用）
  if (error.message === 'Network Error') {
    const t = namespaceT('common.error');
    throw new Error(t('networkError'));
  }

  throw error;
}
