<template>
  <div
    class="pima-nav-form-wrap "
  >
    <div
      v-if="navBar"
      class="w-120"
    />

    <slot />

    <NavBar
      v-if="navBar"
      :nav-item-list="navBar.list"
      :active-item-key="activeNavItemKey"
    />

    <div
      v-if="$slots.right"
      class="w-280"
    >
      <slot name="right" />
    </div>
    <div
      v-else
      class="w-180"
    />
  </div>
</template>


<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, toRefs } from 'vue';
import { findScrollParentElement, createScrollListener } from '@/utils/dom';
import NavBar from './nav-bar.vue';

interface Props {
  navBar: NavigationBar;
}

const props = withDefaults(defineProps<Props>(), {
  navBar: null,
});

const { navBar } = toRefs(props);
const activeNavItemKey = ref('');
const vm = getCurrentInstance();

function addParentScrollListener() {
  if (!navBar.value) {
    return;
  }

  const scrollParent = findScrollParentElement(vm.proxy.$el);
  scrollParent.addEventListener('scroll', createScrollListener(() => {
    const { scrollTop } = scrollParent;
    for (let i = 0; i < navBar.value.list.length; i += 1) {
      const item = navBar.value.list[i];
      const { offsetTop } = item.getInfo(scrollParent);
      if (scrollTop <= (offsetTop - navBar.value.containerBottomBlockingHeight)) {
        activeNavItemKey.value = item.key;
        break;
      }
    }
  }, 50));
}

function refresh() {
  const scrollParent = findScrollParentElement(vm.proxy.$el);
  const { scrollTop } = scrollParent;
  for (let i = 0; i < navBar.value.list.length; i += 1) {
    const item = navBar.value.list[i];
    const { offsetTop, offsetHeight } = item.getInfo(scrollParent);
    if (Math.ceil(scrollTop) <= (Math.ceil(offsetTop) + offsetHeight)) {
      activeNavItemKey.value = item.key;
      break;
    }
  }
}

onMounted(() => {
  addParentScrollListener();
});

defineExpose({
  refresh,
});
</script>
