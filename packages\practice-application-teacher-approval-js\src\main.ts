import { createApp, type App } from 'vue';
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia';
import { qiankunWindow } from 'vite-plugin-qiankun/es/helper';
import ViewUiPlus from 'view-ui-plus';
import 'v3-infinite-loading/lib/style.css';

import AppComp from './app.vue';
import '@/styles/utils.less';
import '@/styles/app.less';

import { i18n } from '^/i18n';
import { registerQiankun } from '@/plugins/qiankun';
import { injectEngineeringThemeIViewCss, injectViewUiPlusCss } from '@/helps/inject';
import { InjectPrototypePlugin } from '@/plugins/inject-prototype';


let app: App | undefined;

function render(container?: HTMLElement | string) {
  const pinia = createPinia();

  app = createApp(AppComp);
  app.use(i18n);
  app.use(PiniaVuePlugin);
  app.use(pinia);
  app.use(InjectPrototypePlugin); // 全局方法
  app.use(ViewUiPlus, {
    i18n,
  });
  app.mount(container ?? '#app');
}

// 独立运行时
// eslint-disable-next-line no-underscore-dangle
if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
  injectViewUiPlusCss();
  injectEngineeringThemeIViewCss();
  render();
} else {
  registerQiankun({ render });
}
