<script lang="ts" setup>
import DefaultText from './default-text.vue';

withDefaults(defineProps<{
  label?: string;
  labelWidth?: number;
  columnFlex?: boolean;
  value?:string | number | undefined;
}>(), {
  label: '',
  labelWidth: 100,
  columnFlex: false,
  value: undefined,
});
</script>


<template>
  <div
    class="detail-pair-label-item"
    :class="{'column-flex': columnFlex}"
  >
    <div
      v-if="label"
      class="label"
      :style="{'width': `${labelWidth}px`}"
      v-text="label"
    />
    <div
      class="content"
    >
      <slot />

      <DefaultText
        v-if="!$slots.default"
        :text="value"
      />
    </div>
  </div>
</template>


<style lang="less" scoped>
.detail-pair-label-item {
  display: flex;
  gap: 22px;
  align-items: flex-start;
  font-size: 15px;
  line-height: 23px;

  &:not(:last-child) {
    margin-bottom: 10px;
  }

  &.column-flex {
    flex-direction: column;
    gap: 12px;
  }

  .label {
    width: 100px;
    color: #8d8e8f;
    font-weight: 400;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
  }

  .content {
    flex: 1;
    max-width: 100%;
    color: #3b3c3d;
    font-weight: 400;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
  }
}
</style>
