import common from '@/i18n/en-US/common';
import accountError from '@/i18n/en-US/account-error';
import paginator from '@/i18n/en-US/paginator';
import dateFormat from '@/i18n/en-US/date-format';
import iview from '@/i18n/en-US/iview';
import consts from './consts';
import apiErrors from './api-errors';

import practiceApply from './practice-apply';
import practiceApprove from './practice-approve';
import changeApprove from './change-approve';
import myPractice from './my-practice';
import practiceApplyManage from './practice-apply-manage';
import practiceContentManage from './practice-content-manage';
import feedback from './feedback';
import positionStatistics from './position-statistics';
import practiceStatistics from './practice-statistics';
import awardRecommend from './award-recommend';
import awardManage from './award-manage';
import engineeringPracticeRoles from './engineering-practice-roles';
import engineeringPracticeUsers from './engineering-practice-users';

const root = {
  title: 'Professional Practice Management',
  enableJavaScriptTips: `Sorry, if JavaScript is not enabled, the website will not function properly. 
    Please turn it on and continue.`,

  common,
  accountError,
  paginator,
  dateFormat,
  consts,
  apiErrors,
  ...iview,

  practiceApply,
  practiceApprove,
  changeApprove,
  myPractice,
  practiceApplyManage,
  practiceContentManage,
  feedback,
  positionStatistics,
  practiceStatistics,
  awardRecommend,
  awardManage,
  engineeringPracticeRoles,
  engineeringPracticeUsers,
};

export default root;
