<script lang="ts" setup>
import { ref } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { PracticeApprovePassApi } from '@/api/practice-approve/approve-pass';

import PimaModal from '@/components/common/pima-modal.vue';


const props = withDefaults(defineProps<{
  modelValue: boolean;
  detailId: number | string | null;
}>(), {});

interface EmitType {
  (e: 'update:modelValue', value: boolean): void
  (e: 'on-success'): void
}


const emit = defineEmits<EmitType>();

const t = namespaceT('practiceApprove');
const tm = namespaceT('common');

const saving = ref(false);

function onClose() {
  emit('update:modelValue', false);
}

async function onConfirm() {
  try {
    saving.value = true;
    const api = new PracticeApprovePassApi({ id: props.detailId });
    await api.send();
    openToastSuccess(t('hint.saveSucc'));
    emit('on-success');
    onClose();
  } catch (error) {
    openToastError(error.message);

    throw error;
  } finally {
    saving.value = false;
  }
}

</script>


<template>
  <PimaModal
    :title="t('title.pass')"
    :value="modelValue"
    :loading="saving"
    :confirm-text="tm('action.ok')"
    size="small"
    v-bind="$attrs"
    @confirm="onConfirm"
    @cancel="onClose"
  >
    <div class="block">
      {{ t('hint.passConfirm') }}
    </div>
  </PimaModal>
</template>
