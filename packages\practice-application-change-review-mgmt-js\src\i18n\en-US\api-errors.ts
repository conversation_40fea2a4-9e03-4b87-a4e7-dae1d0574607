export default {
  common: {
    NO_EXISTS: 'Data not found',
    FREQUENT_OPERATIONS: 'Too many attempts. Please try again later',
  },

  export: {
    NO_EXISTS: 'No data available for export',
    // eslint-disable-next-line max-len
    EXPORT_DATA_LIMIT: 'The exported data records exceed the limit. This system allows exporting a maximum of 1,048,576 records at a time. Please adjust the quantity of data to be exported ',
  },

  attachmentUpload: {
    ATTACHMENT_NAME_LENGTH: 'The attachment name exceeds 30 characters. Please modify it and upload again',
  },

  practiceApplyDraft: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  practiceApplySubmit: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    FREQUENT_OPERATIONS: '@:apiErrors.common.FREQUENT_OPERATIONS',
    LOCK_STATUS_ERROR: '@:apiErrors.practiceApplyUnlock.LOCK_STATUS_ERROR',
  },

  practiceApplyDetail: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  practiceApplyDelete: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    STATUS_ABNORMAL: 'Status error. Only applications in “draft” status can be deleted',
  },

  practiceApplyUnlock: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    LOCK_STATUS_ERROR: 'Invalid lock status, please try again later',
  },

  practiceApplyChange: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    FREQUENT_OPERATIONS: '@:apiErrors.common.FREQUENT_OPERATIONS',
    STATUS_ABNORMAL: 'Status error. Only applications in “approved” status can be modified',
    ALREADY_EXISTS: 'There is an existing pending change request on this application',
    // eslint-disable-next-line max-len
    EXIST_ONGOING_PROCESSES: 'The student has an ongoing process under the current professional practice, and no further actions can be performed',
    // eslint-disable-next-line max-len
    PRACTICE_APPLY_UPDATE_FAIL_RECORD_STATUS_IS_ERROR: 'This application’s process record is in “pending on-campus supervisor review”, “pending off-campus supervisor review”, or “rejected” status and cannot be modified',
    // eslint-disable-next-line max-len
    PRACTICE_APPLY_UPDATE_FAIL_SUMMARY_STATUS_IS_ERROR: 'The summary under this application is in “pending on-campus supervisor review”, “pending off-campus supervisor review”, or “rejected” status and cannot be modified',
    // eslint-disable-next-line max-len
    PRACTICE_APPLY_UPDATE_FAIL_REVIEW_DOC_STATUS_IS_ERROR: 'Review materials under this application are in “rejected” or “pending evaluation” status and cannot be modified',
    LOCK_STATUS_ERROR: '@:apiErrors.practiceApplyUnlock.LOCK_STATUS_ERROR',
    CHANGE_APPLY_EXIST_PENDING: 'There is an ongoing change request in the application, process record, or summary',
  },

  practiceApproveDetail: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  practiceApprove: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    STATUS_ABNORMAL: 'Status error',
    APPROVAL_STATUS_ERROR: 'Approval status error, please try again later',
    LOCK_STATUS_ERROR: '@:apiErrors.practiceApplyUnlock.LOCK_STATUS_ERROR',
  },

  changeApproveDetail: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
  },

  changeApprove: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    STATUS_ABNORMAL: 'Status error. Only applications in “pending review” status can be reviewed',
    APPROVAL_STATUS_ERROR: '@:apiErrors.practiceApprove.APPROVAL_STATUS_ERROR',
    APPROVAL_NODE_NOT_EXIST: 'Approval node does not exist',
    APPROVAL_NODE_USER_NOT_EXIST: 'The approver for the approval node is missing',
  },

  processSubmit: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    PRACTICE_RECORD_STATUS_ERROR: 'Practice process status error, please try again later',
    PRACTICE_APPLY_REC_CHANGE_NO_PASS_OR_REJECT: 'The application change review has not been approved or rejected',
    // eslint-disable-next-line max-len
    PRACTICE_RECORD_SUBMIT_FAIL_EXIST_ONGOING: 'An ongoing process record application already exists. Please wait for it to be approved before submitting a new one. It is recommended to save as draft',
    PRACTICE_RECORD_HAS_EXIST_DRAFT: '@:myPractice.process.hint.draftDataExists',
    // eslint-disable-next-line max-len
    PRACTICE_RECORD_EXIST_PENDING_CHANGE_APPLY: 'There is already a pending change request for the current process record',
  },

  processApprove: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    APPROVAL_STATUS_ERROR: 'Approval status error, please try again later',
  },

  conclusionSubmit: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    PRACTICE_SUMMARY_STATUS_ERROR: 'Practice summary status error, please try again later',
    PRACTICE_APPLY_REC_CHANGE_NO_PASS_OR_REJECT: 'The application change review has not been approved or rejected',
    // eslint-disable-next-line max-len
    PRACTICE_SUMMARY_SUBMIT_FAIL_RECORD_STATUS_IS_ERROR: 'All process records must be approved before submitting the practice summary',
    PRACTICE_SUMMARY_HAS_EXIST: 'A practice summary already exists under this application, please try again later',
    // eslint-disable-next-line max-len
    PRACTICE_SUMMARY_EXIST_PENDING_CHANGE_APPLY: 'There is an ongoing change request under review for the current practice summary',
  },

  reviewMaterial: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    REVIEW_DOC_STATUS_ERROR: 'Review material status error, please try again later',
    REVIEW_DOC_HAS_EXIST: 'Review materials already exist under this application, please try again later',
    PRACTICE_APPLY_REC_CHANGE_NO_PASS_OR_REJECT: 'The application change review has not been approved or rejected',
    // eslint-disable-next-line max-len
    REVIEW_DOC_SUBMIT_FAIL_SUMMARY_STATUS_IS_ERROR: 'Practice summary must be submitted and approved before submitting review materials',
  },

  feedback: {
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    FEEDBACK_STATUS_ERROR: 'Feedback status error, please try again later',
  },

  systemManageRoles: {
    USER_ID_INVALID_FIELD: 'Session expired. Please log in again.',
    NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    ALREADY_EXISTS: 'Role code already exists.',
    BUSY: 'This role has been assigned to users. Please remove the users assigned to the role before deleting it.',
  },
};
