export default {
  searchBar: {
    cultivationProjectPlace: 'All training programs',
    practiceProgNoPlace: 'All course codes',
    keyword: 'Please enter student ID or name',
    userName: 'Student name',
    userNo: 'Student ID',
    practiceProgNo: 'Course code',
    cultivationProject: 'Training program name',
    practiceUnit: 'The institution where you practiced',
    practicePlace: 'Location',
    onCampusSupervisorName: 'On-campus supervisor',
    offCampusSupervisorName: 'Off-campus supervisor',
    reviewDocStatus: 'Grade Assessment Status',
  },

  columns: {
    nameAndNo: '@:practiceApprove.columns.nameAndNo',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    projectInfo: '@:practiceApprove.columns.projectInfo',
    practiceUnit: '@:practiceApply.columns.practiceUnit',
    practicePlace: '@:practiceApply.columns.practicePlace',
    practiceStartDate: 'Professional practice start date',
    practiceEndDate: 'Professional practice end date',
    pendingCount: 'Pending information',
    evalResult: 'Assessment Result',
    evalResultStatus: 'Grading Status',
  },

  label: {
    rejectReason: 'Reason of rejection ',
    passRemark: 'Remarks',
  },

  placeholder: {
    rejectReason: 'Input content',
  },

  title: {
    practiceContentManage: 'Student submission management',
  },

  action: {
    view: 'Student submission management',
  },

  text: {
    pendingCount: 'There are {num} items pending',
  },
};
