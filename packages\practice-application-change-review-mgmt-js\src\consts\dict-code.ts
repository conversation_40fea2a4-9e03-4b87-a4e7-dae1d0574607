export enum DictCode {
  PRACTICE_APPLY_STATUS = 'PRACTICE_APPLY_REC_STATUS', // 实践申请状态
  PRACTICE_APPLY_LOCK_STATUS = 'PRACTICE_APPLY_REC_LOCK_STATUS', // 实践申请锁定状态
  PRACTICE_CLASS_NO = 'PRACTICE_APPLY_REC_PRACTICE_PROG_NO', // 专业实践课程号
  PRACTICE_PROJECT_NAME = 'PRACTICE_APPLY_REC_CULTIVATION_PROJECT', // 培养项目名称
  BUSINESS_TYPE = 'PRACTICE_APPLY_REC_PRACTICE_UNIT_TYPE', // 实践单位类型
  APPROVER_STATUS = 'APPROVER_STATUS', // 实践审批状态
  ACTION_LOG_APPROVAL_STEP = 'ACTION_LOG_APPROVAL_STEP', // 审批节点名称
  ACTION_LOG_ACTION = 'ACTION_LOG_ACTION', // 审批操作
  CHANGE_TYPE = 'CHANGE_REQUEST_REC_TYPE', // 实践异动类型
  CHANGE_REQUEST_REC_STATUS = 'CHANGE_REQUEST_REC_STATUS', // 实践异动记录状态
  PRACTICE_RECORD_STATUS = 'PRACTICE_RECORD_STATUS', // 实践过程状态
  PRACTICE_SUMMARY_STATUS = 'PRACTICE_SUMMARY_STATUS', // 实践总结状态
  REVIEW_DOC_STATUS = 'REVIEW_DOC_STATUS', // 审查材料状态
  FEEDBACK_STATUS = 'FEEDBACK_STATUS',
  AWARD_RECOMMEND_STATUS = 'AWARD_RECOMMEND_STATUS', // TODO:实践奖推荐状态
  AWARD_NAME = 'AWARD_NAME', // TODO:实践奖名称
  AWARD_MANAGE_STATUS = 'AWARD_MANAGE_STATUS', // 实践奖管理状态
}
