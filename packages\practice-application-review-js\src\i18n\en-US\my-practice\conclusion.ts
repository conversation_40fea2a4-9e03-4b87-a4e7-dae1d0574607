export default {
  columns: {
    practiceTitle: 'Practice topic',
    practiceProgNo: '@:myPractice.applyMod.columns.practiceProgNo',
    researchDirection: '@:myPractice.applyMod.columns.researchDirection',
    practiceUnit: '@:myPractice.applyMod.columns.practiceUnit',
    practicePlace: '@:myPractice.applyMod.columns.practicePlace',
    changeStatus: '@:myPractice.applyMod.columns.changeStatus',
    createInfo: '@:myPractice.applyMod.columns.createInfo',
  },

  action: {
    view: '@:myPractice.applyMod.action.view',
    addConclusion: 'Click to add practice summary',
    cancel: '@:common.action.cancel',
    save: '@:common.action.save',
    modify: '@:common.action.modify',
    draft: '@:myPractice.process.action.draft',
    submit: '@:myPractice.process.action.submit',
    pass: '@:myPractice.process.action.pass',
    reject: '@:myPractice.process.action.reject',
  },

  title: {
    add: 'Add practice summary',
    practiceConclusion: 'Practice summary',
    edit: '@:myPractice.process.title.edit',
    view: '@:myPractice.process.title.view',
    restart: '@:myPractice.process.title.restart',
    changeApply: '@:myPractice.process.title.changeApply',
    actionLogTitle: 'Practice summary workflow status',
    changeActionLogTitle: 'Practice summary change workflow status',
  },

  hint: {
    saveDraftSucc: '@:myPractice.process.hint.saveDraftSucc',
    submitSucc: '@:myPractice.process.hint.submitSucc',
    delSuccess: '@:myPractice.process.hint.delSuccess',
    delTitle: '@:myPractice.process.hint.delTitle',
    delConfirm: '@:myPractice.process.hint.delConfirm',
    approveSuccess: '@:myPractice.process.hint.approveSuccess',
    saveSucc: '@:myPractice.process.hint.saveSucc',
  },

  label: {
    userNo: '@:myPractice.process.label.userNo',
    userName: '@:myPractice.process.label.userName',
    practiceTitle: 'Practice topic',
    practiceUnit: 'The institution where you practiced',
    practiceProgNo: '@:practiceApply.form.label.practiceProgNo',
    cultivationProject: '@:practiceApply.form.label.cultivationProject',
    supervisor: 'Practice supervisor',
    practiceTime: 'Practice duration',
    practiceAim: 'Purpose and significance of the practice',
    practiceContent: 'Main content of the practice',
    practiceAchievement: 'Main achievements of the practice',
    modifyContent: '@:practiceApply.form.label.modifyContent',
    attachment: '@:myPractice.process.label.attachment',
  },

  placeholder: {
    modifyContent: '@:practiceApply.form.placeholder.modifyContent',
  },

  print: {
    title: 'Summary Report of Professional Practice in Tsinghua Shenzhen International Graduate School',

    coverFooter: 'Made by Tsinghua Shenzhen International Graduate School',
    // eslint-disable-next-line max-len
    tip: 'After the completion of the professional practice, this form must be submitted to the Engineering Education Center of the Academic Affairs Office for archiving before submitting the graduation application.',

    tableTitle: [
      '1. Purpose and significance',
      '2. Main content',
      '3. Main results',
      'Practice summary attachment:',
      '4. Student statement',
      '5. Evaluation of professional practice',
    ],

    label: {
      projectName: 'Subject',
      userName: 'Name',
      userNo: 'Student ID',
      mainFields: 'Research area',
      supervisor: 'Supervisor',
      practiceUnit: 'Institution/company',
      practiceTime: 'Practice period',
      practiceProgNo: 'Professional practice course number',
      applyTime: 'Form completion date',
      onCampusRemark: 'Practice institution: evaluation',
      offCampusRemark: 'Practice company: evaluation',
      businessRemark: 'Primary supervisor comments: ',
      projectRemark: 'Review of the project guidance committee:',
    },

    text: {
      attachmentRefer: 'Please refer to the attachment',
      statement: `I declare that the contents of the report strictly comply with the relevant provisions
        on the protection of intellectual property rights in schools and practice units, comply with academic
        professional ethics, and do not copy others' practice results.`,
      sign: '(signature):',
      seal: '(签章):',
      time: [
        'month', 'day', 'year',
      ],
      score: 'Evaluation of achievement: ',
      qualified: 'qualified',
      unqualified: 'unqualified',
      inChargeSign: 'Chairperson signature: ',
      participantSign: 'Committee members signature: ',
    },
  },
};
