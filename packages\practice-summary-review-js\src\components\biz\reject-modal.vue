<script lang="ts" setup>
import { ref, reactive, watch, toRef } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import PimaModal from '@/components/common/pima-modal.vue';
import RejectForm from './reject-form.vue';

import { createModel } from '@/models/approve-form-type';
import type { ApproveFormType } from '@/types/approve-form-type';


const props = withDefaults(defineProps<{
  modelValue: boolean;
  saving: boolean;
}>(), {});

interface EmitType {
  (e: 'update:modelValue', value: boolean): void
  (e: 'on-success', model: ApproveFormType): void
}


const emit = defineEmits<EmitType>();

const t = namespaceT('myPractice.process');
const tm = namespaceT('common');
const shown = toRef(props, 'modelValue');
const formRef = ref();

const model = reactive(createModel());

function onClose() {
  emit('update:modelValue', false);
}

async function onConfirm() {
  await formRef.value.onSave();
}

async function onSave() {
  emit('on-success', model);
}

watch(shown, (val) => {
  if (val) {
    Object.assign(model, createModel());
    formRef.value.resetFields();
  }
});
</script>


<template>
  <PimaModal
    :title="t('action.reject')"
    :value="modelValue"
    :loading="saving"
    :confirm-text="tm('action.ok')"
    v-bind="$attrs"
    @confirm="onConfirm"
    @cancel="onClose"
  >
    <div class="block">
      <RejectForm
        ref="formRef"
        v-model="model"
        @on-save="onSave()"
      />
    </div>
  </PimaModal>
</template>
