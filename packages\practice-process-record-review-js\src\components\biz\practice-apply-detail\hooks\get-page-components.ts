import type { InfoBlock } from '@/types/info-block';
import { createInfoBlock } from '@/helps/create-info-block';
import { namespaceT } from '@/helps/namespace-t';

import BaseInfo from '../components/base-info.vue';
import TuitionMember from '../components/tuition-member.vue';
import PracticeContent from '../components/practice-content.vue';


export function getPageComponents(): InfoBlock[] {
  const t = namespaceT('practiceApply.form.navTitle');

  const pageComponents = [
    createInfoBlock(t('baseInfo'), 0, BaseInfo, 'baseInfo'),
    createInfoBlock(t('tuitionMember'), 2, TuitionMember, 'tuitionMember'),
    createInfoBlock(t('practiceContent'), 3, PracticeContent, 'content'),
  ];
  return pageComponents;
}
