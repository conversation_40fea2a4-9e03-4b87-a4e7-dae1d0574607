<template>
  <div class="pima-form-wrapper">
    <slot />

    <div
      v-if="$slots.action"
      class="pima-form-action-wrapper"
    >
      <slot name="action" />
    </div>

    <Spin
      v-if="loading"
      size="large"
      class="pima-spin"
      fix
    />
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';


export default defineComponent({
  name: 'WrapperForm',

  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
});
</script>
