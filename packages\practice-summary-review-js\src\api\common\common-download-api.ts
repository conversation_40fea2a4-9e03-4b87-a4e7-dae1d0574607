import { LOCAL_VITE_ENGINEERING_PRACTICE_API_BASE_URL, API_SALT } from '@/config/api';
import { LOCALE_COOKIE_KEY } from '^/config/cookie';
import { BaseDownloadRequestApi } from '^/api/base/base-download-request-api';


export class CommonDownload<PERSON>pi extends BaseDownloadRequestApi {
  constructor(args?) {
    super({
      baseURL: LOCAL_VITE_ENGINEERING_PRACTICE_API_BASE_URL,
      salt: API_SALT,
      localeCookieKey: LOCALE_COOKIE_KEY,
      timeout: 0,
      ...args,
    });
  }
}
