<script setup lang="ts">

import { namespaceT } from '@/helps/namespace-t';
import type { SupervisorsType } from '@/types/supervisors';

import DetailLabelItem from '@/components/common/detail-label-item.vue';

withDefaults(defineProps<{
  model: SupervisorsType
}>(), {});


const tl = namespaceT('practiceApply.form.label.tutor');

</script>


<template>
  <DetailLabelItem
    :label="tl('name')"
    :label-width="120"
  >
    <template v-if="model.userName">
      {{ model.userName }}
    </template>
  </DetailLabelItem>

  <DetailLabelItem
    :label="tl('jobTitle')"
    :label-width="120"
  >
    <template v-if="model.jobTitle">
      {{ model.jobTitle }}
    </template>
  </DetailLabelItem>

  <DetailLabelItem
    :label="tl('researchDomain')"
    :label-width="120"
  >
    <template v-if="model.researchDomain">
      {{ model.researchDomain }}
    </template>
  </DetailLabelItem>

  <DetailLabelItem
    :label="tl('workUnit')"
    :label-width="120"
  >
    <template v-if="model.workUnit">
      {{ model.workUnit }}
    </template>
  </DetailLabelItem>

  <DetailLabelItem
    :label="tl('mobile')"
    :label-width="120"
  >
    <template v-if="model.mobile">
      {{ model.mobile }}
    </template>
  </DetailLabelItem>

  <DetailLabelItem
    :label="tl('email')"
    :label-width="120"
  >
    <template v-if="model.email">
      {{ model.email }}
    </template>
  </DetailLabelItem>
</template>
