import detail from './detail';
import applyMod from './apply-mod';
import process from './process';
import conclusion from './conclusion';
import review from './review';

export default {
  searchBar: {
    keyword: '请输入校内导师、校外导师、实践单位',
  },

  title: {
    practiceContentManage: '@:myPractice.action.practiceContentManage',
    changeLogTitle: '实践申请单异动流转情况',
  },

  action: {
    feedback: '专业实践问题反馈',
    practiceContentManage: '实践内容管理',
  },

  columns: {
    practiceProgNo: '@:practiceApply.columns.practiceProgNo',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    cultivationProject: '@:practiceApply.columns.cultivationProject',
    practiceUnit: '@:practiceApply.columns.practiceUnit',
    practicePlace: '@:practiceApply.columns.practicePlace',
    practiceStartDate: '专业实践开始日期',
    practiceEndDate: '专业实践结束日期',
  },

  detail,
  applyMod,
  process,
  conclusion,
  review,
};
