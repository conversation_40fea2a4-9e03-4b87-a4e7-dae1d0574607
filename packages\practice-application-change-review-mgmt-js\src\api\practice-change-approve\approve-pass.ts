import { CommonApi } from '@/api/common/common-api';

import type { RequestMethod } from '@/api/base/base-request-api';

import { namespaceT } from '@/helps/namespace-t';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
  STATUS_ABNORMAL: 'STATUS_ABNORMAL',
  APPROVAL_STATUS_ERROR: 'APPROVAL_STATUS_ERROR',
  APPROVAL_NODE_NOT_EXIST: 'APPROVAL_NODE_NOT_EXIST',
  APPROVAL_NODE_USER_NOT_EXIST: 'APPROVAL_NODE_USER_NOT_EXIST',
});

export class ChangeApprovePassApi extends CommonApi<{ model: unknown }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/change-request-rec/approvers/${this.id}/pass`;
  }

  method(): RequestMethod {
    return 'POST';
  }

  async send() {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.changeApprove');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
        case ErrorCode.STATUS_ABNORMAL:
        case ErrorCode.APPROVAL_STATUS_ERROR:
        case ErrorCode.APPROVAL_NODE_NOT_EXIST:
        case ErrorCode.APPROVAL_NODE_USER_NOT_EXIST:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
