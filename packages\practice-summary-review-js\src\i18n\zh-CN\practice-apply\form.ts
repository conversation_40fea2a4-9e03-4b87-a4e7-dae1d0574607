export default {
  title: '实践申请',
  detail: '实践申请详情',

  selectSchoolTutor: {
    selectTutor: '选择导师',
    selectSchoolTutor: '选择校内导师',
    selectExternalTutor: '选择校外导师',
    reselect: '重新选择',
  },

  navTitle: {
    baseInfo: '学生基本信息',
    tuitionMember: '指导小组成员',
    practiceContent: '内容及计划',
  },

  subTitle: {
    schoolTutor: '校内导师',
    externalTutor: '校外导师',
    practiceUnit: '实践单位',
    progFinishInfo: '培养计划中课程部分完成情况',
    practiceOpenTime: '申请开展专业实践起止时间',
    practiceContent: '实践计划与内容',
    practiceProgress: '实践进度',
  },

  label: {
    userNo: '学号',
    userName: '姓名',
    mobile: '联系电话',
    email: '邮件地址',
    cultivationProject: '培养项目名称',
    freshGraduateInd: '入学前是否应届生',
    freshGraduateIndNotice: '(请根据实际情况进行选择)',
    practiceProgNo: '专业实践课程号',
    tutorName: '导师',
    researchDirection: '研究方向',
    tutor: {
      name: '姓名',
      jobTitle: '职称',
      researchDomain: '主要从事的专业与研究领域',
      workUnit: '所在工作单位',
      mobile: '联系电话',
      email: '邮箱地址',
    },
    practiceUnit: {
      name: '名称',
      practiceUnitType: '企业类型',
      practiceUnitInCharge: '负责人姓名',
      practiceUnitInChargePhone: '负责人联系电话',
      practicePlace: '所在地点',
    },
    modifyContent: '修改内容说明',
  },

  placeholder: {
    user: '请输入姓名或学号进行匹配检索',
    modifyContent: '请输入本次申请修改所调整的内容范围，方便导师进行审核',
    chooseActualSituation: '请根据实际情况进行选择',
  },

  text: {
    practiceUnit: '请确保是可以开具实习证明的实践单位，同时请严格按照企查查显示的注册名称填写，确保与营业执照信息完全一致。',
    tuitionMember: '1、你的申请将会以邮件的方式发送给导师、请务必填写！\n2、如未确认校外导师、可先不填写、待确认后再填写',
    totalWeeks: [
      '共计',
      '周',
    ],
  },

  hint: {
    maxLength: '最多输入{max}个字符',
  },
};
