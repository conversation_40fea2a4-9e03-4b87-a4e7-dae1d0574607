<script lang="ts" setup>
import { reactive, ref, watch, toRef } from 'vue';
import { namespaceT } from '@/helps/namespace-t';

import PimaModal from '@/components/common/pima-modal.vue';
import PassForm from './pass-form.vue';

import { createModel } from '@/models/approve-form-type';
import type { ApproveFormType } from '@/types/approve-form-type';


const props = withDefaults(defineProps<{
  modelValue: boolean;
  saving: boolean;
}>(), {});

interface EmitType {
  (e: 'update:modelValue', value: boolean): void
  (e: 'on-success', model: ApproveFormType): void
}


const emit = defineEmits<EmitType>();

const t = namespaceT('myPractice.process');
const tm = namespaceT('common');

const model = reactive(createModel());
const shown = toRef(props, 'modelValue');
const formRef = ref();

function onClose() {
  emit('update:modelValue', false);
}

async function onConfirm() {
  const valid = await formRef.value.validate();

  if (valid) {
    emit('on-success', model);
  }
}

watch(shown, (val) => {
  if (val) {
    Object.assign(model, createModel());
    formRef.value.resetFields();
  }
});
</script>


<template>
  <PimaModal
    :title="t('title.pass')"
    :value="modelValue"
    :loading="saving"
    :confirm-text="tm('action.ok')"
    v-bind="$attrs"
    @confirm="onConfirm"
    @cancel="onClose"
  >
    <PassForm
      ref="formRef"
      v-model="model"
    />
  </PimaModal>
</template>
