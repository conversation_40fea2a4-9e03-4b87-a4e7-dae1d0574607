import { CommonApi } from '@/api/common/common-api';

import type { PracticeProcessModelType } from '@/types/practice-process';
import { namespaceT } from '@/helps/namespace-t';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
});


export class TeacherApproveDetailApi extends CommonApi<{ model: PracticeProcessModelType }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/teacher/practice-record-approvers/${this.id}`;
  }

  async send(): Promise<{ model: PracticeProcessModelType }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.practiceApplyDetail');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
