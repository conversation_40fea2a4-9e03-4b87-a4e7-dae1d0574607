import { CommonApi } from '@/api/common/common-api';

import type { ChangeApproveDetailApiType } from '@/types/practice-change-approve';
import { namespaceT } from '@/helps/namespace-t';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
});


export class ChangeApproveDetailApi extends CommonApi<{ model: ChangeApproveDetailApiType }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/change-request-rec/approvers/${this.id}`;
  }

  async send(): Promise<{ model: ChangeApproveDetailApiType }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.changeApproveDetail');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
