<script lang='ts'>
import { computed, defineComponent, nextTick, ref, useAttrs, useSlots, watch } from 'vue';

import { namespaceT } from '@/helps/namespace-t';

const ModalSize = Object.freeze({
  large: 900,
  default: 800,
  small: 480,
});


export default defineComponent({
  name: 'PimaModal',

  props: {
    value: Boolean,

    loading: Boolean,

    contentLoading: Boolean, // 主题内容加载中，显示Spin

    size: {
      type: [String, Number],
      default: 'large',
    },

    title: {
      type: String,
      default: undefined,
    },

    confirmText: {
      type: String,
      default: '',
    },

    can: {
      type: Boolean,
      default: true,
    },

    showCancel: {
      type: Boolean,
      default: true,
    },

    className: {
      type: String,
      default: undefined,
    },

    confirmBtnDisabled: {
      type: Boolean,
      default: false,
    },
  },

  emits: [
    'confirm',
    'cancel',
    'update:modelValue',
  ],

  setup(props, { emit }) {
    const slots = useSlots();

    const t = namespaceT();
    const tm = namespaceT('common');

    const modalBodyBlockRef = ref();

    function onCancel() {
      emit('update:modelValue', false);
      emit('cancel');
    }

    function onConfirm() {
      emit('confirm');
    }

    function getThemeClass() {
      const classList = ['pima-modal-wrapper'];

      // iview Modal的class-name属性只接受string
      return classList.join(' ').concat(` ${props.className || ''}`);
    }

    const themeClass = computed(() => getThemeClass());

    const width = computed(() => {
      if (typeof props.size === 'number') {
        return props.size;
      }
      return ModalSize[props.size];
    });

    function scrollTop() {
      if (modalBodyBlockRef.value.parentNode.scrollTop > 0) {
        modalBodyBlockRef.value.parentNode.scrollTop = 0;
      }
    }

    watch(() => props.value, (val) => {
      if (val) {
        nextTick(() => {
          scrollTop();
        });
      }
    });

    return {
      attrs: useAttrs(),
      slots,
      themeClass,
      width,
      modalBodyBlockRef,

      onCancel,
      onConfirm,
      t,
      tm,
    };
  },
});
</script>


<template>
  <Modal
    :model-value="value"
    :class-name="themeClass"
    :mask-closable="false"
    :width="width"
    :title="title"
    v-bind="attrs"
    @on-cancel="onCancel"
  >
    <div ref="modalBodyBlockRef">
      <slot />
    </div>

    <template
      #footer
    >
      <slot
        v-if="slots.footer"
        name="footer"
      />

      <div
        v-else
        class="action"
      >
        <Button
          v-if="showCancel"
          class="pima-btn"
          :disabled="loading"
          @click="onCancel()"
        >
          {{ t('common.action.cancel') }}
        </Button>

        <Button
          v-if="can"
          type="primary"
          class="pima-btn"
          :loading="loading"
          :disabled="confirmBtnDisabled"
          @click="onConfirm()"
        >
          {{ confirmText || tm('action.confirm') }}
        </Button>
      </div>
    </template>

    <Spin
      v-show="contentLoading"
      large
      fix
    />
  </Modal>
</template>
