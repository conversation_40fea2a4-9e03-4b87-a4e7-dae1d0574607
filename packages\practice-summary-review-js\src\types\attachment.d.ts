export interface AttachmentVO {
  /**
   * 上传时间
   */
  createTime?: string;
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 文件后缀
   */
  fileExt?: string;
  /**
   * 文件新名称
   */
  fileName?: string;
  filePath?: AttachmentFilePathVO;
  /**
   * 文件大小
   */
  fileSize?: number;
  /**
   * 原路径
   */
  fileUrl?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 文件原名
   */
  origFileName?: string;
  /**
   * 关联ID
   */
  relateId?: number;
  /**
   * 关联类型(leave:请假-休假ID)
   */
  relateType?: string;
  /**
   * 排序
   */
  sortOrder?: number;
  /**
   * 操作时间
   */
  updateTime?: string;
  /**
   * 更新人名称
   */
  updateUserName?: string;
}

/**
 * AttachmentFilePathVO，附件-文件路径VO
 */
export interface AttachmentFilePathVO {
  /**
   * 正常大小
   */
  normal?: string;
  /**
   * 小图
   */
  small?: string;
  // 规范大小后的图
  custom?: string;
}
