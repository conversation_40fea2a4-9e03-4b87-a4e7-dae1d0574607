<script lang="ts" setup>
import { ref, reactive, nextTick, onMounted, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';

import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { createPracticeProcessModel } from '@/models/practice-process';
import { handleProcessDetailData } from '@/helps/handle-process-data';
import { appendEditorCss } from '@/helps/append-editor-css';
import { useQiankunStore } from '@/store/qiankun';
import { TeacherApproveDetailApi } from '@/api/teacher-approve/detail';
import { TeacherApproveActionLogApi } from '@/api/teacher-approve/action-log';
import { TeacherApproveRejectApi } from '@/api/teacher-approve/reject';
import { TeacherApprovePassApi } from '@/api/teacher-approve/pass';

import WithModalContent from '@/components/biz/with-modal-content.vue';
import WithWrapperForm from '@/components/biz/with-wrapper-form.vue';
import ModalReject from '@/components/biz/reject-modal.vue';
import ModalPass from '@/components/biz/pass-modal.vue';

import { ActionLogType } from '@/types/action-log';
import type { PracticeProcessModelType } from '@/types/practice-process';


const qiankunStore = useQiankunStore();
const { dataId, isToDo, isApprove } = storeToRefs(qiankunStore);

const t = namespaceT('myPractice.process');
const actionLog = ref<ActionLogType[]>([]);
const model = reactive<PracticeProcessModelType>(createPracticeProcessModel());
const loading = ref<boolean>(false);
const rejectModal = reactive({
  shown: false,
  saving: false,
});

const passModal = reactive({
  shown: false,
  saving: false,
});

function onCancel() {
  if (!isToDo.value) {
    qiankunStore.onCancel();
  } else {
    qiankunStore.goBack();
  }
}

function onSuccess() {
  if (!isToDo.value) {
    qiankunStore.onSuccess();
  }
}

async function onLoadDetail() {
  const api = new TeacherApproveDetailApi({ id: dataId.value });
  const res = await api.send();
  Object.assign(model, handleProcessDetailData(res.model));
}

async function loadActionLog() {
  const api = new TeacherApproveActionLogApi({ id: dataId.value });
  const res = await api.send();
  actionLog.value = res.data;
}

function onReject() {
  rejectModal.shown = true;
}

function onPass() {
  passModal.shown = true;
}

async function onConfirmReject(val) {
  try {
    rejectModal.saving = true;
    const api = new TeacherApproveRejectApi({
      id: dataId.value,
    });
    api.data = val;
    await api.send();
    openToastSuccess(t('hint.approveSuccess'));
    rejectModal.shown = false;
    onCancel();
    onSuccess();
  } catch (error) {
    openToastError(error.message);
    throw error;
  } finally {
    rejectModal.saving = false;
  }
}

async function onConfirmPass(val) {
  try {
    passModal.saving = true;
    const api = new TeacherApprovePassApi({
      id: dataId.value,
    });
    api.data = val;
    await api.send();
    openToastSuccess(t('hint.approveSuccess'));
    passModal.shown = false;
    onCancel();
    onSuccess();
  } catch (error) {
    openToastError(error.message);
    throw error;
  } finally {
    passModal.saving = false;
  }
}

onBeforeMount(() => {
  appendEditorCss();
});

onMounted(async () => {
  await nextTick();

  loading.value = true;
  Promise.all([
    onLoadDetail(),
    loadActionLog(),
  ]).catch((error) => {
    openToastError(error.message);
  }).finally(() => {
    loading.value = false;
  });
});
</script>


<template>
  <div>
    <!-- 专业实践应用 -->
    <WithModalContent
      v-if="!isToDo"
      :model-value="model"
      :action-log="actionLog"
      :is-approve="isApprove"
      @on-cancel="onCancel"
      @on-reject="onReject"
      @on-pass="onPass"
    />

    <!-- 待办pc显示 -->
    <WithWrapperForm
      v-else
      :loading="loading"
      :model-value="model"
      :action-log="actionLog"
      @on-cancel="onCancel"
      @on-reject="onReject"
      @on-pass="onPass"
    />

    <ModalReject
      v-model="rejectModal.shown"
      :saving="rejectModal.saving"
      :z-index="1500"
      @on-success="onConfirmReject"
    />

    <ModalPass
      v-model="passModal.shown"
      :saving="passModal.saving"
      :z-index="1500"
      @on-success="onConfirmPass"
    />
  </div>
</template>
