import type { AttachmentVO } from './attachment';


export interface PracticeConclusionModelType {
  id?: number;
  practiceApplyRecId: number;
  userNo: string;
  userName: string;
  practiceTitle: string;
  practiceUnit: string;
  cultivationProject: string;
  tutor: string;
  practiceStartDate: string;
  practiceEndDate: string;
  practiceProgNo: string;
  practiceAim: string;
  practiceContent: string;
  practiceAchievement: string;
  attachmentList: AttachmentVO[];
  attachmentIdList: number[];
  submitUserName: string;
  submitTime: string;
  status: string;
  canRemove: boolean;
  canApplyUpdate: boolean;
  canReSubmit: boolean;
  canUpdate: boolean;
  canPrint: boolean;
  modifyContent: string;
  canApproval: boolean;
}
