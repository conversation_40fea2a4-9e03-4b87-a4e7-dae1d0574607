import type { ChangeApproveDetailType, ChangeApproveDetailApiType } from '@/types/practice-change-approve';
import { handlePracticeDetailData } from '@/helps/handle-practice-detail-data';
import { handleProcessDetailData } from '@/helps/handle-process-data';
import { handleConclusionDetailData } from '@/helps/handle-conclusion-data';


export function handleDetailData(data: ChangeApproveDetailApiType): ChangeApproveDetailType {
  return {
    id: data.id,
    canApprove: data.canApprove,
    relateType: data.relateType,
    changePracticeApplyRecDetails: handlePracticeDetailData({
      ...data.changePracticeApplyRecDetails,
      modifyContent: data.modifyContent,
    }),
    changePracticeRecordDetails: handleProcessDetailData({
      ...data.changePracticeRecordDetails,
      modifyContent: data.modifyContent,
    }),
    changePracticeSummaryDetails: handleConclusionDetailData({
      ...data.changePracticeSummaryDetails,
      modifyContent: data.modifyContent,
    }),
  };
}
