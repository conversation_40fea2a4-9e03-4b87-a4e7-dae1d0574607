import type { ActionLogType } from '@/types/action-log';
import { CommonApi, RequestParams } from '@/api/common/common-api';


export class TeacherApproveActionLogApi extends CommonApi<{ data: Array<ActionLogType> }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/teacher/practice-record-approvers/${this.id}/action-logs`;
  }

  defaultParams(): RequestParams {
    return {
      page: 1,
      limit: 99999,
    };
  }
}
