<script lang="ts" setup>
import { ref, reactive, nextTick, onMounted, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';

import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { handleConclusionDetailData } from '@/helps/handle-conclusion-data';
import { createPracticeConclusionModel } from '@/models/practice-conclusion';
import { appendEditorCss } from '@/helps/append-editor-css';
import { useQiankunStore } from '@/store/qiankun';
import { TeacherApprovalDetailApi } from '@/api/teacher-approve/detail';
import { TeacherApprovalActionLogApi } from '@/api/teacher-approve/action-log';
import { TeacherApprovalRejectApi } from '@/api/teacher-approve/reject';
import { TeacherApprovalPassApi } from '@/api/teacher-approve/pass';

import WithModalContent from '@/components/biz/with-modal-content.vue';
import WithWrapperForm from '@/components/biz/with-wrapper-form.vue';
import ModalReject from '@/components/biz/reject-modal.vue';
import ModalPass from '@/components/biz/pass-modal.vue';


const qiankunStore = useQiankunStore();
const { dataId, isApprove, isToDo } = storeToRefs(qiankunStore) || {};

const t = namespaceT('myPractice.conclusion');
const actionLog = ref([]);
const model = reactive(createPracticeConclusionModel());
const loading = ref(false);
const rejectModal = reactive({
  shown: false,
  saving: false,
});

const passModal = reactive({
  shown: false,
  saving: false,
});

function onCancel() {
  if (!isToDo.value) {
    qiankunStore.onCancel();
  } else {
    qiankunStore.goBack();
  }
}

function onSuccess() {
  if (!isToDo.value) {
    qiankunStore.onSuccess();
  }
}

async function onLoadDetail() {
  try {
    loading.value = true;
    const api = new TeacherApprovalDetailApi({ id: dataId.value });
    const res = await api.send();
    Object.assign(model, {
      ...handleConclusionDetailData(res.model),
    });
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
}

async function loadActionLog() {
  const api = new TeacherApprovalActionLogApi({ id: dataId.value });
  const res = await api.send();
  actionLog.value = res.data;
}

function onReject() {
  rejectModal.shown = true;
}

function onPass() {
  passModal.shown = true;
}

async function onConfirmReject(val) {
  try {
    rejectModal.saving = true;
    const api = new TeacherApprovalRejectApi({
      id: dataId.value,
    });
    api.data = val;
    await api.send();
    openToastSuccess(t('hint.approveSuccess'));
    rejectModal.shown = false;
    onCancel();
    onSuccess();
  } catch (error) {
    openToastError(error.message);
    throw error;
  } finally {
    rejectModal.saving = false;
  }
}

async function onConfirmPass(val) {
  try {
    passModal.saving = true;
    const api = new TeacherApprovalPassApi({
      id: dataId.value,
    });
    api.data = val;
    await api.send();
    openToastSuccess(t('hint.approveSuccess'));
    passModal.shown = false;
    onCancel();
    onSuccess();
  } catch (error) {
    openToastError(error.message);
    throw error;
  } finally {
    passModal.saving = false;
  }
}

onBeforeMount(() => {
  appendEditorCss();
});

onMounted(async () => {
  await nextTick();

  Promise.all([
    onLoadDetail(),
    loadActionLog(),
  ]).catch((error) => {
    openToastError(error.message);
  });
});
</script>


<template>
  <div>
    <!-- 专业实践应用 -->
    <WithModalContent
      v-if="!isToDo"
      :is-approve="isApprove"
      :model-value="model"
      :action-log="actionLog"
      @on-cancel="onCancel"
      @on-reject="onReject"
      @on-pass="onPass"
    />

    <!-- 待办pc显示 -->
    <WithWrapperForm
      v-else
      :loading="loading"
      :model-value="model"
      :action-log="actionLog"
      @on-cancel="onCancel"
      @on-reject="onReject"
      @on-pass="onPass"
    />

    <ModalReject
      v-model="rejectModal.shown"
      :saving="rejectModal.saving"
      :z-index="1500"
      @on-success="onConfirmReject"
    />

    <ModalPass
      v-model="passModal.shown"
      :saving="passModal.saving"
      :z-index="1500"
      @on-success="onConfirmPass"
    />
  </div>
</template>
