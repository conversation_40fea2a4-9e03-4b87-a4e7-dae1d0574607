

declare global {
  interface NavItem {
    key: string;
    order: number;
    title: string;
    scrollIntoView: () => void;
    getInfo: (parent: HTMLElement) => { offsetTop: number; offsetHeight: number };
  }

  interface NavigationBar {
    push(item: Omit<NavItem, 'key'> & Partial<Pick<NavItem, 'key'>>): void;
    list: NavItem[]
    reset(): void;
    containerBottomBlockingHeight: number;
  }
}

export {};
