import { CommonApi } from '@/api/common/common-api';

import type { PracticeApplyApiDetailType } from '@/types/practice-approve';
import { namespaceT } from '@/helps/namespace-t';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
});


export class PracticeApproveDetailApi extends CommonApi<{ model: PracticeApplyApiDetailType }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/teacher/approvers/${this.id}`;
  }

  async send(): Promise<{ model: PracticeApplyApiDetailType }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.practiceApproveDetail');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
