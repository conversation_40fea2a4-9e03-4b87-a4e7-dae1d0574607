<script lang="ts" setup>
import { namespaceT } from '@/helps/namespace-t';


const t = namespaceT('myPractice.conclusion');
const emit = defineEmits(['on-cancel', 'on-reject', 'on-pass']);
</script>


<template>
  <Button
    class="pima-btn"
    @click="emit('on-cancel')"
  >
    {{ t('action.cancel') }}
  </Button>

  <Button
    type="primary"
    class="pima-btn ml-16"
    @click="emit('on-reject')"
  >
    {{ t('action.reject') }}
  </Button>

  <Button
    type="primary"
    class="pima-btn ml-16"
    @click="emit('on-pass')"
  >
    {{ t('action.pass') }}
  </Button>
</template>
