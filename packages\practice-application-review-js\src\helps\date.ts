import _ from 'lodash';
import { parse, isValid, format, startOfDay, isBefore, isAfter } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { ApiDateFormat, API_SERVER_TIME_ZONE } from '@/config/api';
import { i18n } from '@/i18n/index';


/** @function
 * 转换为日期
 * @param {(Date|string|number)} dateString - 要解析的字符串或数字
 * @param {string} [formatString="yyyy-MM-dd'T'HH:mm:ssxxx"] - 转换的格式
 * @returns {(Date|undefined)} 日期
 */
export function dateConvert(dateString: Date | string | number, formatString: string = ApiDateFormat.DATE_TIME) {
  if (dateString instanceof Date) {
    return dateString;
  }

  if (typeof dateString === 'string') {
    const date = parse(dateString, formatString, new Date());
    if (isValid(date)) {
      return date;
    }
  } else if (typeof dateString === 'number') {
    const date = new Date(dateString);
    if (isValid(date)) {
      return date;
    }
  }

  return undefined;
}

/** @function
 * 日期格式化（本地时区）
 * @param {(Date|string|number)} dateString - 要格式化的字符串或数字日期
 * @param {string} destFormatString - 输出时的格式
 * @param {string} [parseFormatString="yyyy-MM-dd"] - 转换时的格式
 * @returns {string} 日期格式化后的字符串
 */
export function dateFormat(
  dateString: Date | string | number,
  destFormatString: string = i18n.global.t('dateFormat.date'),
  parseFormatString: string = ApiDateFormat.DATE,
): string {
  const date = dateConvert(dateString, parseFormatString);
  return isValid(date) ? format(date, destFormatString) : '';
}

export const dateFormatCR = _.curryRight(dateFormat);

/** @function
 * 日期格式化（带服务器时区，固定在+8000时区）
 * @param {(Date|string|number)} dateString - 要格式化的字符串或数字日期
 * @param {string} destFormatString - 输出时的格式
 * @param {string} [parseFormatString="yyyy-MM-dd'T'HH:mm:ssxxx"] - 转换时的格式
 * @param {string} [serverTimeZone="Asia/Shanghai"] - 转换时的服务器时区
 * @returns {string} 日期格式化后的字符串
 */
export function dateFormatSTZ(
  dateString: Date | string | number,
  destFormatString: string = i18n.global.t('dateFormat.dateTime'),
  parseFormatString: string = ApiDateFormat.DATE_TIME,
  serverTimeZone: string = API_SERVER_TIME_ZONE,
): string {
  const date = dateConvert(dateString, parseFormatString);
  return isValid(date) ? formatInTimeZone(date, serverTimeZone, destFormatString) : '';
}

export const dateFormatSTZCR = _.curryRight(dateFormatSTZ);

// 提取小时分钟方法
export function extractHourMinute(timeString: string) {
  const regex = /(\d{2}:\d{2})(:\d{2})?/;
  const matched = regex.exec(timeString);
  if (matched?.length > 1) {
    return matched[1];
  }

  return '';
}

// 日期字符串补秒和时间
export function padRightMinuteTimeZone(dateString: string) {
  return `${dateString}:00+08:00`;
}

export function isBeforeDay(date1: Date, date2: Date) {
  if (!(isValid(date1) && isValid(date2))) {
    return false;
  }

  const d1 = startOfDay(date1);
  const d2 = startOfDay(date2);

  return isBefore(d1, d2);
}

export function isAfterDay(date1: Date, date2: Date) {
  if (!(isValid(date1) && isValid(date2))) {
    return false;
  }

  const d1 = startOfDay(date1);
  const d2 = startOfDay(date2);

  return isAfter(d1, d2);
}

export function getTomorrow() {
  const tomorrow = new Date();
  tomorrow.setTime(tomorrow.getTime() + 24 * 60 * 60 * 1000);
  return tomorrow;
}
