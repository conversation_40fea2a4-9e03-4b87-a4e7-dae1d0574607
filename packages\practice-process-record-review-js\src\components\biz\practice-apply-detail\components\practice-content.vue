<script setup lang="ts">
import { computed, onMounted, nextTick } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import type { PracticeApplyContentType } from '@/types/practice';

import { useBusinessTypeStore } from '@/store/business-type';

import DefaultText from '@/components/common/default-text.vue';
import DetailLabelItem from '@/components/common/detail-label-item.vue';
import CellTitle from '@/components/biz/cell-title.vue';

const props = withDefaults(defineProps<{
  content: PracticeApplyContentType;
  changeApply?: boolean;
  isManage?: boolean;
  isTeacher?: boolean;
}>(), {
  changeApply: false,
  isManage: false,
  isTeacher: false,
});


const model = computed(() => props.content);
const tl = namespaceT('practiceApply.form.label.practiceUnit');
const t = namespaceT('practiceApply.form');
const td = namespaceT('dateFormat');

const businessTypeStore = useBusinessTypeStore();

onMounted(async () => {
  await nextTick();
  businessTypeStore.loadDataIfNeeded();
});
</script>


<template>
  <div class="pl-20">
    <CellTitle :title="t('subTitle.practiceUnit')" />
    <DetailLabelItem
      :label="tl('name')"
      :label-width="120"
    >
      <template v-if="model.practiceUnit">
        {{ model.practiceUnit }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('practiceUnitType')"
      :label-width="120"
    >
      <template v-if="model.practiceUnitType">
        {{ businessTypeStore.getTextByCode(model.practiceUnitType) }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('practiceUnitInCharge')"
      :label-width="120"
    >
      <template v-if="model.practiceUnitInCharge">
        {{ model.practiceUnitInCharge }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('practiceUnitInChargePhone')"
      :label-width="120"
    >
      <template v-if="model.practiceUnitInChargePhone">
        {{ model.practiceUnitInChargePhone }}
      </template>
    </DetailLabelItem>

    <DetailLabelItem
      :label="tl('practicePlace')"
      :label-width="120"
    >
      <template v-if="model.practicePlace">
        {{ model.practicePlace }}
      </template>
    </DetailLabelItem>

    <CellTitle
      :title="t('subTitle.progFinishInfo')"
      class="mt-20"
    />
    <DetailLabelItem>
      <div
        v-if="model.progFinishInfo"
        class="pre-wrap"
      >
        {{ model.progFinishInfo }}
      </div>
    </DetailLabelItem>

    <CellTitle
      :title="t('subTitle.practiceOpenTime')"
      class="mt-20"
    />
    <DetailLabelItem>
      {{ $dateFormatSTZ(model.practiceDate?.practiceStartDate, td('date')) }}
      -
      {{ $dateFormatSTZ(model.practiceDate.practiceEndDate, td('date')) }}
      {{ t('text.totalWeeks[0]') }}
      {{ model.practiceWeeks }}
      {{ t('text.totalWeeks[1]') }}
    </DetailLabelItem>

    <CellTitle
      :title="t('subTitle.practiceContent')"
      class="mt-20"
    />
    <DetailLabelItem>
      <div
        v-if="model.practiceContent"
        class="pre-wrap"
      >
        {{ model.practiceContent }}
      </div>
    </DetailLabelItem>

    <CellTitle
      :title="t('subTitle.practiceProgress')"
      class="mt-20"
    />
    <DetailLabelItem>
      <div
        v-if="model.practiceProgress"
        class="pre-wrap"
      >
        {{ model.practiceProgress }}
      </div>
    </DetailLabelItem>

    <!-- 修改内容说明 -->
    <template v-if="changeApply">
      <CellTitle
        :title="t('label.modifyContent')"
        class="mt-20"
      />
      <DetailLabelItem>
        <div
          class="pre-wrap"
          :class="{ 'noticeable': !!model.modifyContent }"
        >
          <DefaultText :text="model.modifyContent" />
        </div>
      </DetailLabelItem>
    </template>
  </div>
</template>

<style scoped lang="less">
.pre-wrap {
  white-space: pre-wrap;
}

.noticeable {
  color: var(--red-color);
}
</style>
