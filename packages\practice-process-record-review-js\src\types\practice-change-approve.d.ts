import type { PracticeDetailApiType, PracticeDetailType } from './practice';
import type { PracticeProcessModelType } from './practice-process';
import type { PracticeConclusionModelType } from './practice-conclusion';

export interface SearchModelType {
  type: string;
  userName: string;
  userNo: string;
  practiceProgNo: string;
  practiceUnit: string;
  cultivationProject: string;
  status: string;
  applyTimeEnd: string;
  applyTimeStart: string;
  keyword: string;
}

export interface PracticeChangeApproveListType {
  id: number;
  userName: string;
  userNo: string;
  onCampusSupervisorName: string;
  offCampusSupervisorName: string;
  type: string;
  practiceProgNo: string;
  practiceUnit: string;
  cultivationProject: string;
  status: string;
  applyTime: string;
  canApprove: boolean;
  updName: string;
  updTime: string;
}

export interface ChangeApproveDetailApiType {
  id: number;
  canApprove: boolean;
  relateType: string;
  changePracticeApplyRecDetails?: PracticeDetailApiType;
  changePracticeRecordDetails?: PracticeProcessModelType,
  changePracticeSummaryDetails?: PracticeConclusionModelType,
  modifyContent: string;
}

export interface ChangeApproveDetailType {
  id: string | number;
  canApprove: boolean;
  relateType: string;
  changePracticeApplyRecDetails?: PracticeDetailType;
  changePracticeRecordDetails?: PracticeProcessModelType,
  changePracticeSummaryDetails?: PracticeConclusionModelType,
}
