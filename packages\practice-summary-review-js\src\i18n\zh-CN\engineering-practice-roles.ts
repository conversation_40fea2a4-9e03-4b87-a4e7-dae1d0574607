export default {
  searchBar: {
    status: '状态',
  },

  title: {
    createRole: '新建角色',
    editRole: '编辑角色',
    authSet: '{name}>>权限设置',
  },

  columns: {
    code: '编码',
    name: '角色名称',
    description: '角色描述',
    isEnable: '状态',
    operatorAndTime: '最后操作人/操作时间',
  },

  action: {
    createRole: '新建角色',
    edit: '编辑',
    authSet: '权限设置',
    del: '删除',
    save: '保存',
  },

  label: {
    code: '角色编码',
    name: '角色名称',
    description: '描述',
    isEnable: '状态',
  },

  placeholder: {
    keyword: '请输入角色编码、角色名称、描述、操作人',
    code: '请输入角色编码',
    name: '请输入角色名称',
    allStatus: '全部状态',
  },

  hint: {
    confirmDelete: '删除后不可恢复，确认删除吗？',
    addSucceeded: '保存成功',
    editSucceeded: '修改成功',
    deleteSuccess: '删除成功',
  },

  tip: {
    isEnable: '停用后，该角色权限将不生效',
  },

  authSet: {
    serviceType: {
      pc: 'PC端',
      weChat: '微信端',
      miniProgram: '小程序端',
    },
    step: {
      authOperate: '权限操作',
      dataRange: '数据可见范围',
    },
    place: {
      chooseDept: '选择部门',
    },
  },
};
