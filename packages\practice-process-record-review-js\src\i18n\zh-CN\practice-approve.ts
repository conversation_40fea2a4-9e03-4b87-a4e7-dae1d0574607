export default {
  title: {
    practiceApprove: '实践申请审核',
    reject: '审核驳回',
    pass: '审核通过',
  },

  searchBar: {
    status: '审核状态',
    statusPlace: '全部审核状态',
    lockStatus: '锁定状态',
    lockStatusPlace: '全部锁定状态',
    keyword: '请输入学生学号、学生名称',
    userName: '学生名称',
    userNo: '学生学号',
    practiceProgNo: '课程号',
    cultivationProject: '培养项目名称',
    practiceUnit: '实践单位',
    practicePlace: '所在地点',
    applyTime: '申请时间',
  },

  action: {
    cancel: '@:common.action.cancel',
    reject: '审核驳回',
    pass: '审核通过',
    view: '查看',
    approve: '审核',
    unlock: '同意解锁',
  },

  columns: {
    nameAndNo: '学生名称/学号',
    projectInfo: '培养项目/课程号',
    status: '审核状态',
    practiceTime: '开始/结束时间',
    practiceWeeks: '总周数',
    practiceUnit: '实践单位/地点',
    onCampusSupervisorName: '校内导师',
    offCampusSupervisorName: '校外导师',
    creTime: '首次申请时间',
    updInfo: '最后操作人/时间',
    lockStatus: '锁定状态',
  },

  hint: {
    saveSucc: '保存成功',
    passConfirm: '确认审核通过吗？',
    unlockTitle: '同意解锁',
    unlockConfirm: '是否同意解锁？',
    unlockSuccess: '解锁成功',
  },

  label: {
    rejectReason: '驳回原因',
  },

  placeholder: {
    rejectReason: '输入内容',
  },
};
