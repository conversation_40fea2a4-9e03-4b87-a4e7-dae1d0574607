<script setup lang="ts">
import { type Ref, ref, computed } from 'vue';

import ContentBlock from '@/components/common/content-block.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';

import type { PracticeDetailType } from '@/types/practice';
import { useNavigationBar } from '@/uses/navigation-bar';
import { namespaceT } from '@/helps/namespace-t';
import { getPageComponents } from './hooks/get-page-components';


interface ComponentRef {
  [key: string]: object;
  validate: () => Promise<boolean>;
}

const props = withDefaults(defineProps<{
  modelValue: PracticeDetailType;
  changeApply?: boolean; // 是否异动
}>(), {
  changeApply: false,
});

interface EmitType {
  (e: 'update:modelValue', val: object): void
}
const emit = defineEmits<EmitType>();

const navBar = useNavigationBar();
const componentRefs: Ref<ComponentRef[]> = ref([]);
const t = namespaceT('practiceApply.form');

const model = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
});

</script>


<template>
  <WrapperFormTitle
    :title="t('detail')"
    class="center-border-wrapper"
  >
    <ContentBlock
      v-for="(block, index) in getPageComponents()"
      :key="index"
      :title="block.title"
      :title-extra="block.hint"
      :order="block.order"
      :nav-bar="navBar"
    >
      <component
        :is="block.component"
        :ref="el => componentRefs[index] = el"
        v-model:[block.modelName]="model[block.modelName]"
        :change-apply="changeApply"
      />
    </ContentBlock>
  </WrapperFormTitle>
</template>


<style lang="less" scoped>
.center-border-wrapper.pima-wrapper-form-title {
  margin: 0 auto 30px;
  border: 1px solid #ededed;
}
</style>
