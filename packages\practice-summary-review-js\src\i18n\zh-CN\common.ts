export default {
  action: {
    search: '搜索',
    advancedSearch: '高级搜索',
    reset: '重置',
    cancel: '取消',
    confirm: '确认',
    ok: '确认',
    selectFile: '选择文件',
    downloadImportTemplate: '下载导入模板',
    add: '添加',
    save: '保存',
    import: '导入',
    export: '导出',
    delete: '删除',
    batchDelete: '批量删除',
    edit: '编辑',
    back: '返回',
    close: '关闭',
    reject: '驳回',
    pass: '通过',
    view: '查看',
    upload: '点击上传',
    reUpload: '重新上传',
    modify: '修改',
    submit: '提交',
  },

  table: {
    serial: '序号',
    createdTime: '创建时间',
    operation: '操作',
    operatorAndTime: '操作人/操作时间',
    checkAll: '全选所有列表结果（共 {total} 条数据）',
  },

  placeholder: {
    search: '搜索',
    select: '请选择',
    input: '请输入',
    all: '全部',
    startDate: '开始日期',
    endDate: '结束日期',
    startTime: '开始时间',
    endTime: '结束时间',
    pleaseInput: '请填写',
    pleaseSelect: '请选择',
    multiKeywordsPlace: '多个检索词用"\\"隔开',
  },

  error: {
    thisFieldIsRequired: '此项为必填项',
    thisFieldMustBeSelected: '此项为必选项',
    formatIsIncorrect: '格式错误',
    emailFormatIsIncorrect: '邮箱格式错误',
    mobileNumberFormatIsIncorrect: '移动电话号码格式错误',
    excelTemplateError: 'Excel模板错误',
    dataDoesNotExist: '数据不存在',
    dataError: '数据有误，请重新选择。 ',
    timeoutError: '请求超时，请刷新重试。 ',
    unknownError: '很抱歉，程序遇到未知错误，请刷新重试。 ',
    networkError: '网络错误，请刷新重试。',
    noAuth: '暂无权限',
    noData: '暂无数据',
    fileTypeError: '文件类型有误，请上传{accept}类型文件',
    fileSizeError: '上传的文件大小不能超过{size}MB，请重新上传',
    fileNumError: '文件个数不能超过{size}',
  },

  hint: {
    hint: '提示',
    loading: '数据加载中',
    noSearchResult: '暂无搜索结果',
    deleting: '删除中...',
    dataSaved: '保存成功',
    savingFailed: '保存失败',
    successfullyDeleted: '删除成功',
    deletionFailed: '删除失败',
    downloadSuccessful: '下载成功',
    downloadFailed: '下载失败',
    importSucceeded: '导入成功',
    importFailed: '导入失败',
    exportSucceeded: '导出成功',
    exportFailed: '导出失败',
    uploadCompleted: '上传成功',
    failedToUpload: '上传失败',
  },

  modal: {
    reminder: '温馨提示',
    areYouSureToDelete: '是否确认删除?',
  },

  operationInfo: {
    lastEditBy: '最后编辑',
    createdBy: '创建人',
  },
};
