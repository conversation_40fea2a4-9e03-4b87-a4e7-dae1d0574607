export default {
  searchBar: {
    startTime: '@:positionStatistics.searchBar.startTime',
    endTime: '@:positionStatistics.searchBar.endTime',
    cultivationProject: '全部培养项目',
    practiceUnit: '全部实践单位',
  },

  title: {
    practiceData: '学生实践数据',
    practiceUnitType: '企业类型分布',
  },

  label: {
    practiceStudents: '实践学生人次',
    totalWeeks: '实践总周数',
    onCampusSupervisor: '校内导师人数',
    offCampusSupervisor: '校外导师人数',
  },

  columns: {
    nameAndNo: '@:positionStatistics.columns.nameAndNo',
    cultivationProject: '@:practiceApply.columns.cultivationProject',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    practiceTime: '实践起止时间',
    practiceWeeks: '实践时长（周）',
    practiceUnit: '@:practiceApply.columns.practiceUnit',
    practiceUnitType: '企业类型',
  },
};
