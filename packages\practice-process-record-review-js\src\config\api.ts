export const LOCAL_BDC_SERVICE_API_BASE_URL = import.meta.env.VITE_BDC_SERVICE_API_BASE_URL; // 统一应用接口API

// eslint-disable-next-line max-len
export const LOCAL_VITE_ENGINEERING_PRACTICE_API_BASE_URL = import.meta.env.VITE_ENGINEERING_PRACTICE_API_BASE_URL; // 专业实践管理接口API

export const API_SALT = 'sigs_engineering_practice'; // 接口盐值

// 接口日期格式
export const ApiDateFormat = Object.freeze({
  DATE_TIME: "yyyy-MM-dd'T'HH:mm:ssxxx",
  DATE: 'yyyy-MM-dd',
});

// 接口所在服务器时区
export const API_SERVER_TIME_ZONE = 'Asia/Shanghai';
