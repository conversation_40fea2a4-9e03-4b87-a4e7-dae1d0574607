<script setup lang="ts">
import { ref, onMounted, reactive, nextTick, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import ActionLog from '@/components/biz/action-log.vue';
import PracticeApplyDetail from '@/components/biz/practice-apply-detail';
import PracticeProcessDetail from '@/components/biz/practice-process-detail.vue';
import PracticeConclusionDetail from '@/components/biz/practice-conclusion-detail.vue';
import NavFormWrap from '@/components/common/nav-form-wrap.vue';

import { ChangeApproveDetailApi } from '@/api/practice-change-approve/detail';
import { ChangeApproveActionLogApi } from '@/api/practice-change-approve/action-log';

import { namespaceT } from '@/helps/namespace-t';
import { openToastError } from '@/helps/toast';
import { createChangeApproveDetail } from '@/models/change-approve';
import type { ActionLogType } from '@/types/action-log';
import { ChangeRelateType } from '@/consts/change-type';
import { useQiankunStore } from '@/store/qiankun';
import { handleDetailData } from './helps/handle-data';
import { appendEditorCss } from '@/helps/append-editor-css';

import RejectModal from '@/components/biz/reject-modal.vue';
import PassModal from '@/components/biz/pass-modal.vue';

const t = namespaceT('changeApprove');
const loading = ref(false);
const model = reactive(createChangeApproveDetail());
const actionLog = ref<Array<ActionLogType>>([]);

const showPassModel = ref(false);
const showRejectModel = ref(false);

const qiankunStore = useQiankunStore();
const { dataId, isToDo } = storeToRefs(qiankunStore);

function onGoBack() {
  qiankunStore.goBack();
}

async function loadActionLog() {
  const api = new ChangeApproveActionLogApi({ id: dataId.value });
  const res = await api.send();
  actionLog.value = res.data;
}

async function loadDetailsInfo() {
  try {
    loading.value = true;
    const api = new ChangeApproveDetailApi({ id: dataId.value });
    const res = await api.send();
    Object.assign(model, handleDetailData(res.model));
    await loadActionLog();
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
}

onBeforeMount(() => {
  appendEditorCss();
});

onMounted(async () => {
  await nextTick();
  loadDetailsInfo();
});

function onReject() {
  showRejectModel.value = true;
}

function onPass() {
  showPassModel.value = true;
}
</script>


<template>
  <div class="engineering-theme engineering-theme-red">
    <div
      class="pima-form-page"
      :class="{'to-do': isToDo}"
    >
      <TitleBar
        go-back
        :title="t('title.changeApprove')"
        @go-back="onGoBack"
      />

      <WrapperForm
        :loading="loading"
      >
        <PracticeApplyDetail
          v-if="model.relateType === ChangeRelateType.PRACTICE_APPLY_REC"
          v-model="model.changePracticeApplyRecDetails"
          :action-log="actionLog"
          :action-log-title="t('title.actionLogTitle')"
          is-teacher
          change-apply
        />

        <NavFormWrap
          v-else-if="model.relateType === ChangeRelateType.PRACTICE_RECORD"
          :nav-bar="null"
        >
          <PracticeProcessDetail
            v-model="model.changePracticeRecordDetails"
            change-apply
            is-teacher
          />

          <template #right>
            <ActionLog
              :data-source="actionLog"
              :title="t('title.processActionLogTitle')"
            />
          </template>
        </NavFormWrap>

        <NavFormWrap
          v-else-if="model.relateType === ChangeRelateType.PRACTICE_SUMMARY"
          :nav-bar="null"
        >
          <PracticeConclusionDetail
            v-model="model.changePracticeSummaryDetails"
            change-apply
            is-teacher
          />

          <template #right>
            <ActionLog
              :data-source="actionLog"
              :title="t('title.conclusionActionLogTitle')"
            />
          </template>
        </NavFormWrap>

        <template
          v-if="model.canApprove"
          #action
        >
          <Button
            class="pima-btn mr-16"
            type="default"
            @click="onGoBack"
          >
            {{ t('action.cancel') }}
          </Button>

          <Button
            class="pima-btn mr-16"
            type="primary"
            @click="onReject"
          >
            {{ t('action.reject') }}
          </Button>

          <Button
            class="pima-btn"
            type="primary"
            @click="onPass"
          >
            {{ t('action.pass') }}
          </Button>
        </template>
      </WrapperForm>

      <RejectModal
        v-model="showRejectModel"
        :detail-id="dataId"
        @on-success="onGoBack"
      />

      <PassModal
        v-model="showPassModel"
        :detail-id="dataId"
        @on-success="onGoBack"
      />
    </div>
  </div>
</template>
