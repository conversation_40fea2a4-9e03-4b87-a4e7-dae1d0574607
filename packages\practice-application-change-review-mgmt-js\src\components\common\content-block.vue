<script setup lang="ts">
import { getCurrentInstance, onMounted, toRef } from 'vue';

import { heightToTop } from '@/utils/dom';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';


interface Props {
  title: string;
  titleExtra?: string;
  order?: number | null;
  navBar?: NavigationBar | null;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  titleExtra: '',
  order: null,
  navBar: null,
});

const vm = getCurrentInstance();
const navBar = toRef(props, 'navBar');

function scrollIntoView() {
  vm.proxy.$el.scrollIntoView({
    behavior: 'smooth',
  });
}

function updateNavBar() {
  if (!navBar.value) {
    return;
  }

  navBar.value.push({
    title: props.title,
    order: props.order,
    scrollIntoView,
    getInfo(targetEle) {
      const offsetTop = heightToTop(vm.proxy.$el, targetEle);
      const { offsetHeight } = vm.proxy.$el;

      return {
        offsetTop,
        offsetHeight,
      };
    },
  });
}

onMounted(() => {
  updateNavBar();
});
</script>


<template>
  <div class="pima-content-block">
    <div class="header-container">
      <div class="header">
        <span class="title">{{ title }}</span>
        <slot name="header-right" />
      </div>

      <div
        v-if="titleExtra"
        class="title-extra"
      >
        <PimaSanitizeHtml
          :inner-html="titleExtra"
        />
      </div>
    </div>

    <div class="wrap">
      <slot />
    </div>
  </div>
</template>
