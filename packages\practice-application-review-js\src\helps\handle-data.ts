import type { PracticeApplyApiDetailType, PracticeApproveDetailType } from '@/types/practice-approve';
import { handlePracticeDetailData } from '@/helps/handle-practice-detail-data';

export function handleDetailData(data: PracticeApplyApiDetailType): PracticeApproveDetailType {
  return {
    id: data.id,
    status: data.status,
    canApprove: data.canApprove,
    changePracticeApplyRecDetails: handlePracticeDetailData(data.practiceApplyRecDetailsVo),
  };
}
