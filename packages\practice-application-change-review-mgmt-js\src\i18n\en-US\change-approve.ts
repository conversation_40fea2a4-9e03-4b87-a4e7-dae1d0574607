export default {
  title: {
    changeApprove: 'Change review',
    changeView: 'Change details',
    reject: '@:practiceApprove.title.reject',
    pass: '@:practiceApprove.title.pass',
    actionLogTitle: 'Practice application form change workflow status',
    processActionLogTitle: 'Practice process change workflow status',
    conclusionActionLogTitle: 'Practice summary change workflow status',
  },

  searchBar: {
    status: '@:practiceApprove.searchBar.status',
    statusPlace: '@:practiceApprove.searchBar.statusPlace',
    historyStatus: 'Status',
    historyStatusPlace: 'All statuses',
    changeType: 'Change type',
    changeTypePlace: 'All change types',
    keyword: '@:practiceApprove.searchBar.keyword',
    userName: '@:practiceApprove.searchBar.userName',
    userNo: '@:practiceApprove.searchBar.userNo',
    practiceProgNo: '@:practiceApprove.searchBar.practiceProgNo',
    cultivationProject: '@:practiceApprove.searchBar.cultivationProject',
    practiceUnit: '@:practiceApprove.searchBar.practiceUnit',
    applyTime: '@:practiceApprove.searchBar.applyTime',
  },

  action: {
    export: '@:common.action.export',
    cancel: '@:common.action.cancel',
    reject: '@:practiceApprove.action.reject',
    pass: '@:practiceApprove.action.pass',
    view: '@:practiceApprove.action.view',
    approve: '@:practiceApprove.action.approve',
  },

  columns: {
    nameAndNo: '@:practiceApprove.columns.nameAndNo',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    practiceProgNo: 'Course code',
    cultivationProject: 'Training program name',
    practiceUnit: 'The institution where you practiced',
    changeType: 'Change type',
    status: 'Review status',
    creTime: 'Application time',
    updInfo: '@:practiceApprove.columns.updInfo',
  },

  hint: {
    saveSucc: '@:practiceApprove.hint.saveSucc',
    passConfirm: '@:practiceApprove.hint.passConfirm',
  },

  label: {
    rejectReason: '@:practiceApprove.label.rejectReason',
  },

  placeholder: {
    rejectReason: '@:practiceApprove.placeholder.rejectReason',
  },
};
