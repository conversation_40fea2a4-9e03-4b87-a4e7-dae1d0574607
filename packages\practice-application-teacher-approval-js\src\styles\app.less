.engineering-theme {
  height: 100%;

  .double-selection-detail.pima-form-page {
    .pima-wrapper-form-title .wrapper-form-title-body {
      .ivu-col:has(.detail-pair-label-item):not(:last-child) {
        margin-bottom: 10px;
      }
    }

    &.to-do {
      height: calc(100vh - 50px);

      .pima-form-wrapper {
        .pima-form-action-wrapper {
          border-radius: 0;
        }
      }
    }
  }
}

@media screen and (width <=768px) {
  .engineering-theme {

    /** double-selection-detail  为了提高样式权重而设置， 可自行修改 */
    .double-selection-detail.pima-form-page {
      height: calc(100vh - constant(safe-area-inset-bottom));
      height: calc(100vh - env(safe-area-inset-bottom));

      &.to-do {
        height: calc(100vh - constant(safe-area-inset-bottom));
        height: calc(100vh - env(safe-area-inset-bottom));
      }

      .pima-title-bar {
        display: none;
      }

      .pima-form-wrapper {
        height: 100%;

        .pima-nav-form-wrap {
          gap: 30px;
          flex-direction: column;
          padding: 20px;

          .w-120 {
            display: none;
          }

          .pima-wrapper-form-title {
            margin: 0;

            .wrapper-form-title-body {
              padding: 20px;
            }
          }

          .w-280 {
            width: 100%;

            .pima-action-log {
              position: static;
              top: 0;
              right: 0;
              width: 100%;
            }
          }
        }

        .pima-form-action-wrapper {
          left: 0;
          padding-bottom: calc(16px + constant(safe-area-inset-bottom));
          padding-bottom: calc(16px + env(safe-area-inset-bottom));
        }
      }

      .engineering-spin {
        z-index: 100;
      }

    }
  }


  .ivu-modal {

    &.reject-modal,
    &.warning-modal {
      width: 480px !important;
    }
  }

}