<script lang="ts" setup>
import ButtonGroup from '@/components/biz/button-group.vue';
import ActionLog from '@/components/biz/action-log.vue';
import PracticeProcessDetail from '@/components/biz/practice-process-detail.vue';

import { namespaceT } from '@/helps/namespace-t';
import type { PracticeProcessModelType } from '@/types/practice-process';
import { ActionLogType } from '@/types/action-log';


type Props = {
  modelValue: PracticeProcessModelType;
  actionLog: ActionLogType[];
  isApprove?: boolean;
};

defineProps<Props>();
const emit = defineEmits(['on-cancel', 'on-reject', 'on-pass']);

const t = namespaceT('myPractice.process');
</script>


<template>
  <div class="with-modal-content">
    <div class="modal-form-content with-action-log">
      <PracticeProcessDetail
        :model-value="modelValue"
        is-teacher
      />

      <ActionLog
        :data-source="actionLog"
        :title="t('title.actionLogTitle')"
      />
    </div>

    <div
      v-if="isApprove"
      class="footer"
    >
      <ButtonGroup
        @on-cancel="emit('on-cancel')"
        @on-reject="emit('on-reject')"
        @on-pass="emit('on-pass')"
      />
    </div>
  </div>
</template>


<style scoped lang="less">
.footer {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 16px;
  text-align: center;
  border-top: 1px solid #e8eaec
}
</style>

<style lang="less">
.ivu-message {
  z-index: 1600 !important;
}
</style>
