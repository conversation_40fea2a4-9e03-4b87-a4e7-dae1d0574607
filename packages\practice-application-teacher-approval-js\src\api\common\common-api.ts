import { useQiankunStore } from '@/store/qiankun';
import { LOCAL_VITE_ENGINEERING_PRACTICE_API_BASE_URL, API_SALT } from '@/config/api';
import { LOCALE_COOKIE_KEY } from '@/config/cookie';
import { type RequestMethod, type RequestParams, type RequestData, BaseRequestApi } from '@/api/base/base-request-api';

export { type RequestMethod, type RequestParams, type RequestData };


export class CommonApi<T> extends BaseRequestApi<T> {
  constructor(args?) {
    const { token } = useQiankunStore();
    const headers = args?.header || {};

    super({
      baseURL: LOCAL_VITE_ENGINEERING_PRACTICE_API_BASE_URL,
      salt: API_SALT,
      localeCookieKey: LOCALE_COOKIE_KEY,
      headers: {
        Authorization: token,
        ...headers,
      },
      ...args,
    });
  }
}
