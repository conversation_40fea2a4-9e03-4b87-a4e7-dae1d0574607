export enum Locale {
  ZH_CN = 'zh_CN',
  EN_US = 'en_US',
  ZH_HK = 'zh_HK',
}

export type LocaleType = typeof Locale[keyof typeof Locale];

const supportLocales: Locale[] = [];
if (import.meta.env.VITE_SUPPORT_LOCALES) {
  const locales = import.meta.env.VITE_SUPPORT_LOCALES.split(',').map((l: Locale) => l.trim() as Locale);
  supportLocales.push(...locales);
} else {
  supportLocales.push(Locale.ZH_CN, Locale.ZH_HK, Locale.EN_US);
}

export const SUPPORT_LOCALES = Object.freeze(supportLocales);

export const FALLBACK_LOCALE = import.meta.env.VITE_FALLBACK_LOCALE || Locale.ZH_CN;
