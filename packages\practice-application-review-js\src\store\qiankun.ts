import { defineStore } from 'pinia';
import type { EventCode } from '@/consts/event-code';


const initialState = {
  dataId: null,
  injectStyled: false,
  token: null,
  jobType: null,
  isToDo: false,
  getToken: () => null,
  goBack: () => {},
  setGlobalStateFn: () => {},
};

const actions = {
  setState({ setGlobalState, ...state }) {
    Object.assign(this.$state, {
      ...state,
      setGlobalStateFn: setGlobalState,
    });
  },

  setGlobalState(eventCode: EventCode, data = {}) {
    this.setGlobalStateFn({
      eventCode,
      payload: data,
    });
  },

  resetGlobalState() {
    this.setGlobalState({
      eventCode: null,
      payload: null,
    });
  },
};

export const useQiankunStore = defineStore('qiankun', {
  state: () => initialState,
  actions,
});
