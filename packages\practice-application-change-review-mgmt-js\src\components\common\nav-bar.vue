<template>
  <div class="pima-nav-bar">
    <div
      v-if="navTitle"
      class="nav-title"
    >
      {{ navTitle }}
    </div>

    <div class="nav-item-wrap">
      <div
        v-for="(item, index) in navItemList"
        :key="index"
        class="nav-item"
        :class="navItemClass(item)"
        @click="scrollIntoView(item)"
      >
        <span class="dot" />
        {{ item.title }}
      </div>
    </div>

    <slot name="footer" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, toRefs, watch, watchEffect } from 'vue';

defineOptions({
  name: 'NavBar',
});

interface Props {
  navTitle?: string;
  navItemList?: Array<NavItem>;
  // 当前激活的导航项唯一标识
  activeItemKey?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  navTitle: '',
  navItemList: () => [],
  activeItemKey: null,
});

const { navItemList, activeItemKey } = toRefs(props);
const currentItemKey = ref('');

function initActiveItem() {
  watchEffect(() => {
    if (navItemList.value.length > 0 && !currentItemKey.value) {
      currentItemKey.value = navItemList.value[0].key;
    }
  });
}

// 用于区分是点击滚动还是滑动
const flag = ref(0);

function scrollIntoView(item) {
  currentItemKey.value = item.key;
  flag.value = 1;
  item.scrollIntoView();
}

function navItemClass({ key }) {
  if (flag.value < 0) {
    return key === activeItemKey.value ? 'active' : '';
  }

  return key === currentItemKey.value ? 'active' : '';
}

watch(
  activeItemKey,
  () => {
    if (flag.value > 0) {
      flag.value = 0;
    } else {
      flag.value -= 1;
    }
  },
);

onMounted(() => {
  initActiveItem();
});
</script>
