import { CommonApi } from '@/api/common/common-api';

import type { RequestData, RequestMethod } from '@/api/base/base-request-api';

import { namespaceT } from '@/helps/namespace-t';
import { ApproveAction } from '@/consts/approve-action';

const ErrorCode = Object.freeze({
  NO_EXISTS: 'NO_EXISTS',
  APPROVAL_STATUS_ERROR: 'APPROVAL_STATUS_ERROR',
});

export class TeacherApproveRejectApi extends CommonApi<{ model: unknown }> {
  id: string;

  constructor({ id }) {
    super({});
    this.id = id;
  }

  url() {
    return `/teacher/practice-record-approvers/${this.id}/approval`;
  }

  method(): RequestMethod {
    return 'POST';
  }

  defaultData(): RequestData {
    return {
      status: ApproveAction.REJECT,
    };
  }

  async send() {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.processApprove');
      switch (error.code) {
        case ErrorCode.NO_EXISTS:
        case ErrorCode.APPROVAL_STATUS_ERROR:
          throw new Error(t(error.code));

        default:
          throw error;
      }
    }
  }
}
