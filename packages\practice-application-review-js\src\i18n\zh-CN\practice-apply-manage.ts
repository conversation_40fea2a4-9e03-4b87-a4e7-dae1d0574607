export default {
  searchBar: {
    statusPlace: '@:practiceApply.searchBar.statusPlace',
    lockStatusPlace: '@:practiceApply.searchBar.lockStatusPlace',
    keyword: '请输入学生学号、学生名称',
    userName: '学生名称',
    userNo: '学生学号',
    practiceProgNo: '@:practiceApply.searchBar.practiceProgNo',
    cultivationProject: '@:practiceApply.searchBar.cultivationProject',
    practiceUnit: '@:practiceApply.searchBar.practiceUnit',
    practicePlace: '@:practiceApply.searchBar.practicePlace',
    status: '@:practiceApply.searchBar.status',
    lockStatus: '@:practiceApply.searchBar.lockStatus',
    applyTime: '@:practiceApply.searchBar.applyTime',
  },

  columns: {
    nameAndNo: '@:practiceApply.columns.nameAndNo',
    projectInfo: '培养项目/课程号',
    status: '@:practiceApply.columns.status',
    practiceTime: '开始/结束时间',
    practiceWeeks: '总周数',
    practiceUnitInfo: '实践单位/地点',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    lockStatus: '@:practiceApply.columns.lockStatus',
    creTime: '@:practiceApply.columns.creTime',
    updInfo: '最后操作人/时间',
  },

  title: {
    practiceApply: '新增实践申请单',
    practiceApplyEdit: '修改申请内容',
  },

  action: {
    view: '@:common.action.view',
    delete: '@:common.action.delete',
    save: '@:common.action.save',
    lock: '锁定',
    unlock: '解锁',
    edit: '修改申请内容',
    editOnCampusTutor: '修改校内导师',
    editOffCampusTutor: '修改校外导师',
    createPracticeApply: '新增实践申请单',
  },

  hint: {
    lockTitle: '锁定',
    lockConfirm: '是否确认锁定？',
    lockSuccess: '锁定成功',
    delTitle: '@:practiceApply.hint.delTitle',
    delConfirm: '@:practiceApply.hint.delConfirm',
    delSuccess: '@:practiceApply.hint.delSuccess',
    unlockTitle: '解锁',
    unlockConfirm: '是否确认解锁？',
    unlockSuccess: '解锁成功',
    saveSucc: '保存成功',
    editValidFail: '该学员当前专业实践业务下，存在进行中的流程，无法进行操作！',
  },
};
