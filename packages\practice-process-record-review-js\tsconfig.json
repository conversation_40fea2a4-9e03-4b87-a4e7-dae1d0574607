{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "target": "ES5",
    "module": "ES2020",
    "strict": false,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "Node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": false,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "~/*": ["node_modules/*"],
    },
     "lib": ["es2015", "esnext", "dom", "dom.iterable", "scripthost"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "types/*.d.ts",
    "vite.config.*.ts"
, "src/consts/input-typets"  ],
}
