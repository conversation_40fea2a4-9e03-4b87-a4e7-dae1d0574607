<template>
  <div class="pima-title-bar">
    <template v-if="goBack">
      <ButtonGoBack
        class="mr-15"
        @click="$emit('go-back')"
      />
    </template>

    <span
      v-if="title"
      class="title"
    >{{ title }}</span>

    <slot />

    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';

import ButtonGoBack from '@/components/common/button-go-back.vue';


export default defineComponent({
  name: 'TitleBar',

  components: {
    ButtonGoBack,
  },

  props: {
    title: {
      type: String,
      default: '',
    },

    goBack: Boolean,
  },

  emits: ['go-back'],
});
</script>
