export default {
  searchBar: {
    startTime: '@:positionStatistics.searchBar.startTime',
    endTime: '@:positionStatistics.searchBar.endTime',
    cultivationProject: 'All training programs',
    practiceUnit: 'All institutions where you practiced',
  },

  title: {
    practiceData: '学生实践数据',
    practiceUnitType: '企业类型分布',
  },

  label: {
    practiceStudents: '实践学生人次',
    totalWeeks: '实践总周数',
    onCampusSupervisor: '校内导师人数',
    offCampusSupervisor: '校外导师人数',
  },

  columns: {
    nameAndNo: '@:positionStatistics.columns.nameAndNo',
    cultivationProject: '@:practiceApply.columns.cultivationProject',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    practiceTime: 'Practice duration',
    practiceWeeks: '实践时长（周）',
    practiceUnit: '@:practiceApply.columns.practiceUnit',
    practiceUnitType: 'Type of enterprise',
  },
};
