import { type AxiosInstance, ResponseType } from 'axios';

import { createRequest } from '@/utils/request';
import { errorInterceptor } from './error-interceptor';


export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS' | 'CONNECT' | 'TRACE';

export type RequestParams = { [key: string]: unknown } | Array<unknown>;

export type RequestData = { [key: string]: unknown } | Array<unknown> | FormData;

export class BaseRequestApi<T> {
  private request: AxiosInstance;

  private targetParams: RequestParams;

  private targetData: RequestData;

  constructor(args) {
    let baseURL: string;
    if ('baseURL' in args) {
      ({ baseURL } = args);
    }

    let timeout: number;
    if ('timeout' in args) {
      ({ timeout } = args);
    }

    let responseType: ResponseType;
    if ('responseType' in args) {
      ({ responseType } = args);
    }

    let headers: Record<string, string>;
    if ('headers' in args) {
      ({ headers } = args);
    }

    let salt: string;
    if ('salt' in args) {
      ({ salt } = args);
    }

    let localeCookieKey: string;
    if ('localeCookieKey' in args) {
      ({ localeCookieKey } = args);
    }

    let onUploadProgress;
    if (args && Object.prototype.hasOwnProperty.call(args, 'onUploadProgress')) {
      if (typeof args.onUploadProgress === 'function') {
        ({ onUploadProgress } = args);
      }
    }

    this.request = createRequest({
      baseURL,
      timeout,
      responseType,
      headers,
      onUploadProgress,
      salt,
      localeCookieKey,
    });

    this.request.interceptors.response.use(
      (response) => response,
      (error) => errorInterceptor(error),
    );

    this.targetParams = this.defaultParams();
    this.targetData = this.defaultData();
  }

  method(): RequestMethod {
    return 'GET';
  }

  url(): string {
    return '';
  }

  defaultParams(): RequestParams {
    return {};
  }

  get params(): RequestParams {
    return this.targetParams;
  }

  set params(value: RequestParams) {
    this.targetParams = {
      ...this.defaultParams(),
      ...value,
    };
  }

  defaultData(): RequestData {
    return {};
  }

  get data(): RequestData {
    return this.targetData;
  }

  set data(value: RequestData) {
    if (value instanceof FormData) {
      this.targetData = value;
      return;
    }

    if (Array.isArray(value)) {
      let defaultData = this.defaultData();
      defaultData = Array.isArray(defaultData) ? defaultData : [defaultData];
      this.targetData = [
        ...defaultData as Array<unknown>,
        ...value,
      ];
      return;
    }

    this.targetData = {
      ...this.defaultData(),
      ...value,
    };
  }

  send(): Promise<T> {
    const method = this.method().toUpperCase();
    const isGetOrHeadMethod = ['GET', 'HEAD'].includes(method);
    const data = isGetOrHeadMethod ? undefined : this.targetData;

    return this.request({
      method,
      data,
      url: this.url(),
      params: this.targetParams,
    });
  }
}
