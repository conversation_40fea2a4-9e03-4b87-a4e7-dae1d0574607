import { isValid } from 'date-fns';
import { isBeforeDay, isAfterDay } from '^/helps/date';


interface GetMinAndMax {
  min: () => Date;
  max: () => Date;
}

/**
 * 该函数基于提供的选择范围和限制范围创建两个函数，
 * 分别用来判断给定的日期是否应该在日期组件中被禁用为最小日期或最大日期。
 *
 * @param {GetMinAndMax} range - 选择的日期范围，包括最小和最大日期的获取函数。
 * @param {GetMinAndMax} limit - 限制的日期范围，用于进一步约束可选日期。
 * @returns 返回一个对象，其中：
 *   - `minDisabled`: 判断并返回布尔值，指示给定日期是否应被禁用为最小可选日期。
 *   - `maxDisabled`: 判断并返回布尔值，指示给定日期是否应被禁用为最大可选日期。
 */
export function useDateRange(range: GetMinAndMax, limit: GetMinAndMax) {
  /**
 * 检查给定的日期是否早于通过函数获取的最小日期。
 *
 * @param date - 需要检查的日期。
 * @param getMin - 返回最小日期的函数，如果未提供则默认为undefined。
 * @returns 如果`date`早于通过`getMin`获得的最小日期且该最小日期有效，则返回true，否则返回false。
 */
  function isBeforeMinDate(date: Date, getMin: () => Date | undefined): boolean {
    if (typeof getMin !== 'function') {
      return false;
    }

    const min = getMin();
    return isValid(min) && isBeforeDay(date, min);
  }

  /**
 * 检查给定的日期是否晚于通过函数获取的最大日期。
 *
 * @param - 需要检查的日期。
 * @param getMax - 返回最大日期的函数，如果未提供则默认为undefined。
 * @returns 如果`date`晚于通过`getMax`获得的最大日期且该最大日期有效，则返回true，否则返回false。
 */
  function isAfterMaxDate(date: Date, getMax: () => Date | undefined): boolean {
    if (typeof getMax !== 'function') {
      return false;
    }

    const max = getMax();
    return isValid(max) && isAfterDay(date, max);
  }

  /**
 * 返回一个配置对象，用于决定日期选择器中哪些日期应被禁用为最小或最大日期。
 *
 * @returns {{minDisabled: Function, maxDisabled: Function}} 返回一个对象，包含：
 * - `minDisabled`: 一个函数，判断给定的日期是否应被禁用为最小选择（基于限制和选择范围）。
 * - `maxDisabled`: 一个函数，判断给定的日期是否应被禁用为最大选择（基于限制和选择范围）。
 */
  return {
  /**
   * 判断给定日期是否应被禁用为最小可选日期。
   * @param {Date} date - 需要检查的日期。
   * @returns {boolean} 布尔值表示日期是否应被禁用。
   */
    minDisabled(date: Date): boolean {
      return isBeforeMinDate(date, limit?.min) || isAfterMaxDate(date, limit?.max) || isAfterMaxDate(date, range.max);
    },

    /**
   * 判断给定日期是否应被禁用为最大可选日期。
   * @param {Date} date - 需要检查的日期。
   * @returns {boolean} 布尔值表示日期是否应被禁用。
   */
    maxDisabled(date: Date): boolean {
      return isBeforeMinDate(date, limit?.min) || isAfterMaxDate(date, limit?.max) || isBeforeMinDate(date, range.min);
    },
  };
}
