import type { AxiosResponse } from 'axios';


function readBlobError(blobData: Blob): Promise<Error> {
  const reader = new FileReader();
  reader.readAsText(blobData);
  return new Promise(((resolve) => {
    // eslint-disable-next-line no-param-reassign
    reader.onload = () => {
      try {
        const result = reader.result as string;
        const data = JSON.parse(result);
        const err = new Error(data.errorMsg);
        Object.assign(err, {
          code: data.errorCode,
          response: { data },
        });
        resolve(err);
      } catch (error) {
        resolve(error);
      }
    };
  }));
}

/**
 * 异步函数：处理Axios请求的响应错误。
 *
 * 本函数旨在解析响应数据，如果数据表示为错误，则抛出错误；否则，返回正常响应数据。
 * 特别地，它处理了Blob类型的数据错误，并对非成功的响应数据进行了错误包装。
 *
 * @param response Axios响应对象，包含服务器返回的所有数据。
 * @returns Promise<AxiosResponse> 如果响应数据不表示错误，则返回原响应数据。
 * @throws 如果响应数据表示错误，则抛出包装后的错误。
 */
export async function interceptorError(response: AxiosResponse): Promise<AxiosResponse> {
  const { data } = response;

  // 检查响应数据是否为Blob类型，且类型不为application/json，这样的数据不被处理为错误
  if (data instanceof Blob) {
    // Blob报错情况处理
    if (!data.type.includes('application/json')) {
      return response;
    }

    // 如果Blob类型的数据是错误的json，尝试读取并抛出错误
    const error = await readBlobError(data);
    throw error;
  }

  // 如果响应数据的success属性为false，表示业务逻辑错误，构造并抛出包装后的错误
  if (!data.success) {
    // 响应状态为失败则抛出错
    const error = new Error(data.errorMsg);
    Object.assign(error, {
      code: data.errorCode,
      response,
    });
    throw error;
  }

  // 如果响应数据没有错误标志，直接返回该数据
  return data;
}
