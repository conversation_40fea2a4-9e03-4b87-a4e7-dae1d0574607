import type { Composer } from 'vue-i18n';
import { renderWithQiankun } from 'vite-plugin-qiankun/es/helper';
import eventBus from '@/utils/event-bus';
import { useQiankunStore } from '@/store/qiankun';
import {
  injectEngineeringThemeIViewCss,
  removeEngineeringThemeIViewCss,
} from '@/helps/inject';
import { i18n } from '^/i18n';
import { FALLBACK_LOCALE } from '@/config/locale';
import { useI18nStore } from '@/store/i18n';

/**
 * @description 注册乾坤
 * @param props
 */
export const registerQiankun = (lifeCycle: {
  render: (container?: HTMLElement | string) => void;
  bootstrap?: () => void;
  update?: () => void;
  unmount?: () => void;
}) => {
  const { render, unmount } = lifeCycle;
  renderWithQiankun({
    bootstrap() {
      // eslint-disable-next-line no-console
      console.log('[Microservice] bootstrap');
    },

    mount(props) {
      // eslint-disable-next-line no-console
      console.log('[Microservice] mount');

      render(props.container);

      const injectState = props.state ?? {};
      const { locale, channel, goBack, onGoBack } = props.state;
      const qiankunStore = useQiankunStore();
      const i18nStore = useI18nStore();

      i18nStore.locale = locale ?? FALLBACK_LOCALE;
      (i18n.global as unknown as Composer).locale.value = i18nStore.locale;

      if (channel !== import.meta.env.VITE_SERVICE_CODE) {
        injectEngineeringThemeIViewCss();
      }

      qiankunStore.setState({
        code: props.name,
        setGlobalState: props.setGlobalState,
        ...injectState,
        isToDo: channel !== import.meta.env.VITE_SERVICE_CODE,
        /** 兼容 移动端 待办 onGoBack */
        goBack: goBack || onGoBack,
      });

      props.onGlobalStateChange((state: Record<string, unknown>) => {
        eventBus.emit('onGlobalStateChange', state);
      }, true);
    },

    update(props) {
      // eslint-disable-next-line no-console
      console.log('[Microservice] update', props);
    },

    unmount() {
      // eslint-disable-next-line no-console
      console.log('[Microservice] unmount');

      const qiankunStore = useQiankunStore();
      qiankunStore.resetGlobalState();
      eventBus.off('onGlobalStateChange');

      if (qiankunStore.isToDo) {
        removeEngineeringThemeIViewCss();
      }

      unmount?.();
    },
  });
};
