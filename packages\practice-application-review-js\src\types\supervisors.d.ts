export interface SupervisorsType {
  userId: string;
  userName: string;
  userNo: string;
  mobile: string;
  email: string;
  jobTitle?: string;
  researchDomain?: string;
  workUnit?: string;
}

export interface SupervisorsListType {
  id: number;
  supervisorType: string;
  supervisorUserId: string;
  supervisorUserNo: string;
  supervisorName: string;
  jobTitle: string;
  researchDomain: string;
  workUnit: string;
  phone: string;
  email: string;
}

export interface TutorSearchModelType {
  name: string;
  mobile: string;
}
