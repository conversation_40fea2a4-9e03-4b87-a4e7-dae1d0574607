<script lang="ts" setup>
import { onMounted, nextTick } from 'vue';

import type { PracticeConclusionModelType } from '@/types/practice-conclusion';
import { namespaceT } from '@/helps/namespace-t';
import { useClassNoStore } from '@/store/classno';
import { usePracticeProjectStore } from '@/store/practice-project';

import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import DetailLabelItem from '@/components/common/detail-label-item.vue';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';
import AttachmentListView from '@/components/biz/attachment-list-view.vue';
import CellTitle from '@/components/biz/cell-title.vue';
import DefaultText from '@/components/common/default-text.vue';


withDefaults(defineProps<{
  changeApply?: boolean;
  isManage?: boolean; // 是否管理端
  isTeacher?: boolean; // 是否教师端
}>(), {
  changeApply: false,
  isManage: false,
  isTeacher: false,
});

const model = defineModel<PracticeConclusionModelType>();
const t = namespaceT('myPractice.conclusion');
const tl = namespaceT('myPractice.conclusion.label');

const classNoStore = useClassNoStore();
const projectStore = usePracticeProjectStore();

onMounted(async () => {
  await nextTick();

  classNoStore.loadDataIfNeeded();
  projectStore.loadDataIfNeeded();
});
</script>


<template>
  <WrapperFormTitle :title="t('title.practiceConclusion')">
    <div class="pl-20">
      <DetailLabelItem
        :label="tl('userNo')"
        :label-width="120"
      >
        {{ model.userNo }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('userName')"
        :label-width="120"
      >
        {{ model.userName }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceTitle')"
        :label-width="120"
      >
        {{ model.practiceTitle }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceUnit')"
        :label-width="120"
      >
        {{ model.practiceUnit }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('cultivationProject')"
        :label-width="120"
      >
        {{ projectStore.getTextByCode(model.cultivationProject) }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('supervisor')"
        :label-width="120"
      >
        {{ model.tutor }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceTime')"
        :label-width="120"
      >
        {{ model?.practiceStartDate }}
        ~
        {{ model.practiceEndDate }}
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceProgNo')"
        :label-width="120"
      >
        <template v-if="model.practiceProgNo">
          {{ classNoStore.getTextByCode(model.practiceProgNo) }}
        </template>
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceAim')"
        :label-width="120"
      >
        <PimaSanitizeHtml
          :inner-html="model.practiceAim"
          class="view"
        />
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceContent')"
        :label-width="120"
      >
        <PimaSanitizeHtml
          :inner-html="model.practiceContent"
          class="view"
        />
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('practiceAchievement')"
        :label-width="120"
      >
        <PimaSanitizeHtml
          :inner-html="model.practiceAchievement"
          class="view"
        />
      </DetailLabelItem>

      <DetailLabelItem
        :label="tl('attachment')"
        :label-width="120"
      >
        <AttachmentListView
          :attachments="model.attachmentList"
        />
      </DetailLabelItem>

      <!-- 修改内容说明 -->
      <template v-if="changeApply">
        <CellTitle
          :title="tl('modifyContent')"
          class="mt-20"
        />
        <DetailLabelItem>
          <div
            class="pre-wrap"
            :class="{ 'noticeable': !!model.modifyContent }"
          >
            <DefaultText :text="model.modifyContent" />
          </div>
        </DetailLabelItem>
      </template>
    </div>
  </WrapperFormTitle>
</template>

<style scoped lang="less">
.pre-wrap {
  white-space: pre-wrap;
}

.noticeable {
  color: var(--red-color);
}
</style>
