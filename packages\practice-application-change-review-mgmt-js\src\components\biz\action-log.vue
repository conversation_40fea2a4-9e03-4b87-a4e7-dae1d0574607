<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';

import { namespaceT } from '@/helps/namespace-t';
import type { ActionLogType } from '@/types/action-log';
import { useActionLogStepStore } from '@/store/action-log-step';
import { useActionLogActionStore } from '@/store/action-log-action';

const props = withDefaults(defineProps<{
  dataSource: ActionLogType[];
  title?: string;
}>(), {
  title: '',
});

const td = namespaceT('dateFormat');
const actionLogBodyRef = ref();

const current = computed(() => props.dataSource.length - 1);
const actionLogStepStore = useActionLogStepStore();
const actionLogActionStore = useActionLogActionStore();


onMounted(async () => {
  await nextTick();
  actionLogStepStore.loadDataIfNeeded();
  actionLogActionStore.loadDataIfNeeded();
});

watch(() => props.dataSource, (val) => {
  if (val) {
    nextTick(() => {
      actionLogBodyRef.value = {
        scrollTop: actionLogBodyRef.value?.scrollHeight,
      };
    });
  }
}, {
  immediate: true,
});
</script>


<template>
  <div class="pima-action-log">
    <div class="pima-action-log-title">
      {{ title }}
    </div>
    <div
      ref="actionLogBodyRef"
      class="pima-action-log-body"
    >
      <Steps
        :current="current"
        direction="vertical"
      >
        <Step
          v-for="(item, idx) in dataSource"
          :key="`action-log${item.id}`"
          :title="actionLogStepStore.getTextByCode(item.approvalStep)"
        >
          <template #content>
            <div class="name-action">
              {{ item.userName }}

              <span class="ml-4">
                {{ actionLogActionStore.getTextByCode(item.action) }}
              </span>
            </div>
            <div class="action-time">
              {{ $dateFormatSTZ(item.actionTime, td('fullDateTime')) }}
            </div>
            <div
              v-if="item.remark"
              class="remark"
            >
              {{ item.remark }}
            </div>
          </template>

          <template #icon>
            <img
              v-if="current === idx"
              src="@/assets/img/icon-step-active.svg"
            >
            <img
              v-else
              src="@/assets/img/icon-step-finished.svg"
            >
          </template>
        </Step>
      </Steps>
    </div>
  </div>
</template>
