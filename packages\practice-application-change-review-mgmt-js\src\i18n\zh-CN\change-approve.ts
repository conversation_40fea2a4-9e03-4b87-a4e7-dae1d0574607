export default {
  title: {
    changeApprove: '异动审核',
    changeView: '异动查看',
    reject: '@:practiceApprove.title.reject',
    pass: '@:practiceApprove.title.pass',
    actionLogTitle: '实践申请单异动流转情况',
    processActionLogTitle: '实践过程记录异动流转情况',
    conclusionActionLogTitle: '实践总结异动流转情况',
  },

  searchBar: {
    status: '@:practiceApprove.searchBar.status',
    statusPlace: '@:practiceApprove.searchBar.statusPlace',
    historyStatus: '状态',
    historyStatusPlace: '全部状态',
    changeType: '异动类型',
    changeTypePlace: '全部异动类型',
    keyword: '@:practiceApprove.searchBar.keyword',
    userName: '@:practiceApprove.searchBar.userName',
    userNo: '@:practiceApprove.searchBar.userNo',
    practiceProgNo: '@:practiceApprove.searchBar.practiceProgNo',
    cultivationProject: '@:practiceApprove.searchBar.cultivationProject',
    practiceUnit: '@:practiceApprove.searchBar.practiceUnit',
    applyTime: '@:practiceApprove.searchBar.applyTime',
  },

  action: {
    export: '@:common.action.export',
    cancel: '@:common.action.cancel',
    reject: '@:practiceApprove.action.reject',
    pass: '@:practiceApprove.action.pass',
    view: '@:practiceApprove.action.view',
    approve: '@:practiceApprove.action.approve',
  },

  columns: {
    nameAndNo: '@:practiceApprove.columns.nameAndNo',
    onCampusSupervisorName: '@:practiceApprove.columns.onCampusSupervisorName',
    offCampusSupervisorName: '@:practiceApprove.columns.offCampusSupervisorName',
    practiceProgNo: '课程号',
    cultivationProject: '培养项目名称',
    practiceUnit: '实践单位',
    changeType: '异动类型',
    status: '审核状态',
    creTime: '申请时间',
    updInfo: '@:practiceApprove.columns.updInfo',
  },

  hint: {
    saveSucc: '@:practiceApprove.hint.saveSucc',
    passConfirm: '@:practiceApprove.hint.passConfirm',
  },

  label: {
    rejectReason: '@:practiceApprove.label.rejectReason',
  },

  placeholder: {
    rejectReason: '@:practiceApprove.placeholder.rejectReason',
  },
};
