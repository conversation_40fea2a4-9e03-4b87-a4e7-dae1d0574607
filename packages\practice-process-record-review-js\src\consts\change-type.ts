// 异动类型
export enum ChangeType {
  PRACTICE_APPLY_CHANGE = 'PRACTICE_APPLY_REC_CHANGE', // 实践申请单异动
  PRACTICE_PROCESS_CHANGE = 'PRACTICE_PROCESS_RECORD_CHANGE', // 实践过程记录异动
  PRACTICE_CONCLUSION_CHANGE = 'PRACTICE_SUMMARY_CHANGE', // 实践总结异动
}

// 异动详情的关联类型
export enum ChangeRelateType {
  PRACTICE_APPLY_REC = 'PRACTICE_APPLY_REC', // 实践申请单异动
  PRACTICE_RECORD = 'PRACTICE_RECORD', // 实践过程记录异动
  PRACTICE_SUMMARY = 'PRACTICE_SUMMARY', // 实践总结异动
}
