<script lang="ts" setup>
import { InputType } from '@/consts/input-type';


const props = withDefaults(defineProps<{
  type?: InputType;
  maxlength?: number | null;
}>(), {
  type: InputType.TEXT,
  maxlength: null,
});

const emit = defineEmits([
  'update:modelValue',
]);


const isTextarea = props.type === InputType.TEXTARER;
const maxLengthByType = isTextarea ? 500 : 255;
const inMaxLength = props.maxlength ? props.maxlength : maxLengthByType;

function getClasses() {
  switch (props.type) {
    case InputType.TEXTARER:
      return 'pima-input-type-textarea';

    default:
      return 'pima-input';
  }
}

function onInput(val) {
  switch (props.type) {
    case InputType.TEXT:
    case InputType.TEXTARER:
      emit('update:modelValue', val ? val.target.value.trim() : val);
      break;
    default:
      emit('update:modelValue', val);
  }
}

function onClear() {
  emit('update:modelValue', '');
}
</script>


<template>
  <Input
    :class="getClasses()"
    :type="type"
    :maxlength="inMaxLength"
    @input="onInput"
    @on-clear="onClear"
  />
</template>
