import type { AttachmentVO } from './attachment';


// 实践过程列表
export interface PracticeProcessListType {
  id: number;
  approverId?: number;
  projectName: string;
  practiceStartDate: string;
  practiceEndDate: string;
  status: string;
  submitUserName: string;
  submitTime: string;
  canRemove: boolean;
  canApplyUpdate: boolean;
  canReSubmit: boolean;
  canUpdate: boolean;
  canSave: boolean;
  canPrint: boolean;
  canView: boolean;
  canApproval: boolean;
}


export interface PracticeProcessModelType {
  id?: number;
  practiceApplyRecId: number;
  practiceProgNo?: string;
  userNo?: string;
  userName?: string;
  projectName: string;
  startTime: string | Date;
  endTime: string | Date;
  content: string;
  attachmentList?: AttachmentVO[];
  attachmentIdList?: number[];
  modifyContent: string;
}
