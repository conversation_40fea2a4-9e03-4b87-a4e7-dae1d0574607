import { type AxiosResponse } from 'axios';
import { saveAs } from 'file-saver';
import { type RequestMethod, type RequestParams, type RequestData, BaseRequestApi } from './base-request-api';


export { type RequestMethod, type RequestParams, type RequestData };

export class BaseDownload<PERSON><PERSON><PERSON><PERSON><PERSON> extends BaseRequestApi<AxiosResponse> {
  constructor(args) {
    super({
      ...args,
      responseType: 'blob',
    });
  }

  async send(): Promise<AxiosResponse> {
    const response = await super.send();
    const { headers, data } = response as AxiosResponse;
    if (data instanceof Blob) {
      const contentDisposition = headers['content-disposition'];
      let fileName = contentDisposition ? decodeURIComponent(contentDisposition.match(/filename=(.+)/)[1]) : 'untitled';
      fileName = fileName.replace(/"/g, '');
      saveAs(data, fileName);
    }

    return response;
  }
}
