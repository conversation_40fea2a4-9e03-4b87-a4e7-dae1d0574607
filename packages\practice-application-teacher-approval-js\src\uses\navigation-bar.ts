import { computed, reactive, ref } from 'vue';
import { nanoid } from 'nanoid';

/**
 * 创建一个导航栏管理器。
 *
 * @param {number} [containerBottomBlockingHeight=50] - 导航栏底部遮挡的高度，默认值为50。
 * @returns {NavigationBar} 导航栏管理器实例。
 */
export function useNavigationBar(containerBottomBlockingHeight: number = 50): NavigationBar {
  const originList = ref([]);

  const list = computed(() => {
    return originList.value.sort((a, b) => {
      return a.order - b.order;
    });
  });

  /**
   * 添加一个元素到导航栏
   * @param {{
   *  title: string,
   *  order: number,
   *  scrollIntoView: function, // 滚动到当前元素
   *  getInfo: function, // 计算当前元素到指定可滚动父元素的上边距及自身高度等信息
   * }} options - 需要添加到导航栏的元素信息
   */
  function push(options: Omit<NavItem, 'key'> & Partial<Pick<NavItem, 'key'>>) {
    if (!options.key) {
      Object.assign(options, {
        key: nanoid(),
      });
    }

    originList.value.push(options);
  }

  function reset() {
    originList.value = [];
  }

  return reactive({
    list,
    push,
    reset,
    containerBottomBlockingHeight,
  });
}
